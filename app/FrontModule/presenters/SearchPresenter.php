<?php declare(strict_types = 1);

namespace FrontModule;

use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SetupCreator\Catalog\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Catalog\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Catalog\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\SearchLog\SearchLog;
use App\Model\Orm\SearchLogDetail\SearchLogDetail;
use App\Model\ParameterModel;
use App\Model\ParameterValueModel;
use App\Model\Tree;
use Elastica\Query\Term;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\IFilterCacheFactory;
use SuperKoderi\Search\QueryParser;
use SuperKoderi\SearchHelper;
use SuperKoderi\SearchService;
use function in_array;
use function is_array;
use function mb_substr;
use function mb_strlen;
use function strlen;
use function trim;

class SearchPresenter extends BasePresenter
{

	/** @persistent */
	public string $search = '';

	/** @inject */
	public ParameterModel $parameterModel;

	/** @inject */
	public ParameterValueModel $parameterValueModel;

	protected array $filterParams = [];

	protected array|null $filterAnimals = null;

	protected int|null $filterAnimal = null;

	/** @inject */
	public \App\Model\ElasticSearch\Common\Repository $elasticTreeRepository;

	/** @inject */
	public ResultReader $commonResultReader;

	/** @inject */
	public BucketFilterFactory $bucketFilterFactory;

	/** @inject */
	public BasicElasticItemListFactory $basicElasticItemListFactory;

	/** @inject */
	public ElasticItemListFactory $elasticItemListFactory;

	/** @inject */
	public  BoxListFactory $boxListFactory;

	/** @inject */
	public  SortCreator $sortCreator;

	public function startup()
	{
		parent::startup();
		$idref = $this->params['idref'];
		$this->setObject($this->orm->tree->getById($idref));
	}

	public function actionDefault(?string $search = null, bool $suggest = false, array $filter = []): void
	{
		$this->filterParams = $filter;

		if ($search === null) {
			$search = $this->request->getParameter('search');
		}
		if (empty($search)) {
			$search = $this->request->getParameters()['filter']['search'] ?? '';
		}

		$this->search = $search;
	}

	public function renderDefault(?string $search = null, bool $suggest = false): void
	{
		$this->template->animalsInfo = $this->getAnimalInfo();

		$sort = $this->sortCreator->create('bestseller', $this->mutation, $this->userEntity);

		if ($search === null) {
			$search = $this->request->getPost('search');
		}
		if (empty($search)) {
			$search = $this->request->getParameters()['filter']['search'] ?? '';
		}

		$trees = new EmptyCollection();
		$blogs = new EmptyCollection();
		$categories = new EmptyCollection();
		$countBlogs = 0;

		$this->logSearch($search);

		$itemsObject = Result::from(
			new EmptyCollection(),  /** @phpstan-ignore-line */
			0,
			0,
		);

		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($this->pages->eshop, $search);
		$elasticItemListGenerator = $this->elasticItemListFactory->create($this->pages->eshop, $this->filterParams, $this->mutation);
		$boxListGenerator = $this->boxListFactory->create($this->pages->eshop);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			$this->mutation,
			20
		);

		if ($search) {
			$search = trim($search);

			$mutation = $this->mutation;
			$this->template->search = $search;

			if ($suggest) {
				$categoryMust = [
					new Term(['type' => 'seolink']),
					new Term(['public' => true]),
					new Term(['hide' => false]),
				];
				$categoryResults = $this->elasticTreeRepository->fulltextSearch($mutation, $categoryMust, $search, 10, minScore: 80);

				$blogMust = [
					new Term(['type' => 'blog']),
					new Term(['public' => true]),
					new Term(['hide' => false]),
				];
				$blogResults = $this->elasticTreeRepository->fulltextSearch($mutation, $blogMust, $search, 10, minScore: 200);

				$itemsObject = $bucketFilter->getItems(30, 0, $sort);

			} else {
				$this->addComponent($this->visualPaginatorFactory->create(), 'pagerArticle');
				$this['pagerArticle']->object = $this->getObject();

				$paginatorArticle = $this['pagerArticle']->getPaginator();
				$paginatorArticle->itemsPerPage = $this->configService->get('articlesPaging');

				$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
				$this['pager']->object = $this->getObject();

				if (isset($this->params['more'])) {
					$this['pager']->setStartPage($this->params['more']);
				}

				$paginator = $this['pager']->getPaginator();
				$paginator->itemsPerPage = $this->configService->get('shop', 'productsPaging');

				$categoryMust = [
					new Term(['type' => 'seolink']),
					new Term(['public' => true]),
					new Term(['hide' => false]),
				];
				$categoryResults = $this->elasticTreeRepository->fulltextSearch($mutation, $categoryMust, $search, 30, minScore: 80);

				$blogMust = [
					new Term(['type' => 'blog']),
					new Term(['public' => true]),
					new Term(['hide' => false]),
				];
				$blogResults = $this->elasticTreeRepository->fulltextSearch($mutation, $blogMust, $search, size: $paginatorArticle->itemsPerPage, offset: $paginatorArticle->offset, minScore: 200);
				$paginatorArticle->itemCount = $blogResults->getTotalHits();
				$countBlogs = $paginatorArticle->itemCount;

				$filter = $bucketFilter->getFilter($this->filterParams);
				$itemsObject = $bucketFilter->getItems($paginator->itemsPerPage, $paginator->offset, $sort);
				$paginator->itemCount = $itemsObject->totalCount;

				$this->template->filter = $filter;
			}

			$categories = $this->commonResultReader->mapResultToEntityCollection($categoryResults, ElasticCommon::TYPE_SEOLINK, 10);
			$blogs = $this->commonResultReader->mapResultToEntityCollection($blogResults, ElasticCommon::TYPE_TREE, 200);
		}

		$this->template->showProductFilter = ($this->filterParams) || $itemsObject->totalCount;
		$this->template->hasResults = $trees->count()
			|| $blogs->count()
			|| $itemsObject->totalCount || $categories->count();

		$this->template->trees = $trees;
		$this->template->blogs = $blogs;
		$this->template->categories = $categories;
		$this->template->products = $itemsObject->items;
		$this->template->countProducts = $itemsObject->totalCount;
		$this->template->countBlogs = $countBlogs;

		$this->template->search = $this->search;
		$this->template->cleanFilterParam = $this->filterParams;
		$this->template->linkSeo = $this->linkSeo;

		$this->template->allParameters = $this->orm->parameter->findAll()->fetchPairs('uid', null);
//		$this->template->tooltips = $this->orm->parameter->findAll()->fetchPairs('uid', 'tooltip');
		$this->template->uidToUnitMap = $this->parameterModel->getUidToUnitMap();
		$this->template->uidToName = $this->parameterModel->getUidToNameMap();
//		$this->template->forceOpen = $this->parameterModel->getForceOpen();
		$this->template->variantParameterValues = $this->parameterValueModel->getVariantParameterValues();

		$this->pushPageDataEvent('search', $this->userEntity);

		$this->template->isOpenInCookie = function ($name) {
			$cookie = $this->httpRequest->getCookie('isOpen' . $name, 0);
			return $cookie;
		};

		if ($suggest) {
			$this->setView('suggest');
		} elseif ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function logSearch(string $search): void
	{
		$searchDB = mb_substr(trim($search), 0, 120);
		while (strlen($searchDB) > 254) { //must fit in CHAR(255) db column
			$searchDB = mb_substr($searchDB, 0, mb_strlen($searchDB) - 5);
		}

		$now = new DateTimeImmutable();
		if (strlen($searchDB) > 0) {
			$log = $this->orm->searchLog->getBy(['mutation' => $this->mutation, 'phrase' => $searchDB]);
			if (!isset($log)) {
				$log = new SearchLog();
				$log->mutation = $this->mutation;
				$log->phrase = $searchDB;
			}

			$log->count += 1;
			$log->lastSearchTime = $now;
			$this->orm->searchLog->persist($log);
		}
	}

	protected function logDeatil(string $search, array $result, array $resProduct, bool $isSuggest): void
	{
		$searchDB = mb_substr(trim($search), 0, 120);
		while (strlen($searchDB) > 254) { //must fit in CHAR(255) db column
			$searchDB = mb_substr($searchDB, 0, mb_strlen($searchDB) - 5);
		}

		if (strlen($searchDB) > 0) {
			$now = new DateTimeImmutable();

			$treeCount = ($result['blog']['count'] ?? 0) + ($result['tree']['count'] ?? 0);
			$seoCount = $result['category']['count'] ?? 0;
			$productCount = $resProduct['product']['count'] ?? 0;
			$sumCount = $productCount + $treeCount + $seoCount;

			$log = new SearchLogDetail();
			$log->mutation = $this->mutation;
			$log->searchTime = $now;
			$log->phrase = $searchDB;
			$log->isSuggest = $isSuggest;
			$log->resultCount = $sumCount;
			$log->productCount = $productCount;
			$log->pageCount = $treeCount;
			$log->seoCount = $seoCount;
			$this->orm->searchLogDetail->persist($log);
		}
	}

}
