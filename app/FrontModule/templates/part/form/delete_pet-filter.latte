<form action="?" class="f-pet-filter u-mb-md js-ajax-form">
	<p class="f-pet-filter__filter u-mb-xs">
		<b class="f-pet-filter__title">
			{_recommended_for}:
		</b>
		<span class="f-pet-filter__items">
			<span class="f-pet-filter__list grid grid--middle grid--y-xxs">
				<span class="f-pet-filter__item grid__cell size--auto">
					<label class="f-pet-filter__label inp-item inp-item--checkbox">
						<input class="f-pet-filter__inp js-auto-submit" type="checkbox" name="pet">
						<span>
							{('check')|icon}
							<span class="avatar avatar--md avatar--name avatar--teal">
								<span class="avatar__inner">
									{('dog')|icon}
								</span>
								Rikki
							</span>
						</span>
					</label>
				</span>
				<span class="f-pet-filter__item grid__cell size--auto">
					<label class="f-pet-filter__label inp-item inp-item--checkbox">
						<input class="f-pet-filter__inp js-auto-submit" type="checkbox" name="pet">
						<span>
							{('check')|icon}
							<span class="avatar avatar--md avatar--name avatar--orange">
								<span class="avatar__inner">
									{('cat')|icon}
								</span>
								Fifinka
							</span>
						</span>
					</label>
				</span>
			</span>
		</span>
	</p>
	<div class="f-pet-filter__products">
		{* {include '../crossroad/products.latte', class=>false} *}
	</div>
</form>
