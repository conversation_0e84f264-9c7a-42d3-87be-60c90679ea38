{default $orderHistory = FALSE}


<div class="c-products-row u-mb-xs u-pt-md">
	<div class="c-products-row__items">
		{* produkty *}
		{if $orderHistory}
			{php $source = $order->items}
		{else}
			{php $source = $order->products}
		{/if}

		{foreach $source as $productOrderItem}
			{if $iterator->isFirst()}
				{include '../box/product-row.latte', product=>$productOrderItem, caption=>true, static=>$orderHistory}
			{else}
				{include '../box/product-row.latte', product=>$productOrderItem, static=>$orderHistory}
			{/if}

			{if !$orderHistory}
				{* v kosiku ... vypiseme darek *}
				{if $productOrderItem->presents && $productOrderItem->presents->count()}
					{foreach $productOrderItem->presents as $presentItem}
						{include '../box/product-row.latte', product=>$presentItem, static=>$orderHistory, isGift=>true}
					{/foreach}
				{/if}
			{/if}

		{/foreach}

		{*vzorky*}
		{if $order->samples && $order->samples->count()}
			{foreach $order->samples as $sItem}
				{include '../box/product-row.latte', product=>$sItem, static=>$orderHistory}
			{/foreach}
		{/if}

{*		*}{* vouchery *}
{*		{foreach $order->vouchers as $voucherOrderItem}*}
{*			{include '../box/product-row-coupon.latte', voucherOrderItem=>$voucherOrderItem}*}
{*		{/foreach}*}

		{* slevy *}
		{foreach $order->discounts as $voucherOrderItem}
			{include '../box/product-row-coupon.latte', voucherOrderItem=>$voucherOrderItem}
		{/foreach}



	</div>
</div>
