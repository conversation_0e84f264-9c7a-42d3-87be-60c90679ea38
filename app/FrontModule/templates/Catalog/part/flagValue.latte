{default $flagEntity = false}

<li class="f-filter__item">
	<label class="inp__item inp__item--checkbox{if !$isActive && $count == 0} inp__item--disabled{/if}">
		{if $type=='simple'}
			<input type="checkbox" name="filter[flags][{$name}]" value="{$value}" {if isset($isActive) && $isActive} checked="checked"{/if}>
		{else}
			<input type="checkbox" class="inp-item__inp" name="filter[flags][{$name}][{$value}]" value="{$value}" {if isset($isActive) && $isActive} checked="checked"{/if}>
		{/if}
		<span>
			{('check')|icon}

			{php $cleanFilterParamCopy = $cleanFilterParam}


			{if $type=='simple'}
				{if $isActive}
					{php unset($cleanFilterParamCopy['flags'][$name])}
				{else}
					{php $cleanFilterParamCopy['flags'][$name] = $value}
				{/if}
			{else}
				{if $isActive}
					{*{dump $cleanFilterParamCopy['flags']}*}
					{*{dump $name}*}
					{*{dump $value}*}
					{php unset($cleanFilterParamCopy['flags'][$name][$value])}
				{else}
					{php $cleanFilterParamCopy['flags'][$name][$value] = $value}
				{/if}
			{/if}




			{capture $link}{link 'this', filter => $cleanFilterParamCopy, 'pager-page' => null}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}
			<a href="{$link}" class="js-filter__checkbox" {if $linkSeo->hasNofollow($link, ['filter' => $cleanFilterParamCopy])}rel="nofollow"{/if}>{if $type=='simple'}{_$name}{else}{$value}{/if}{if $unit} {$unit}{/if}</a>
				<span class="inp__item-count">
					{if !$isActive}
						({if isset($isPlus) && $isPlus}+{/if}{$count})
					{/if}

				</span>
			</span>
	</label>
</li>



