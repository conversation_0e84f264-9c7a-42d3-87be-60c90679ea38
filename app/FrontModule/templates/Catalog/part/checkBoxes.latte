{default $forceOpen = false}

<div class="f-filter__group{if $box->isOpen() || $forceOpen} is-open{/if}" data-controller="filter-group" n:if="$box->getItems() !== []">
	<p class="f-filter__title">
		<button type="button" class="btn f-filter__name" data-action="filter-group#toggle">
			{$box->title}
		</button>
		<span n:if="!empty($box->description)" class="f-filter__info" title="{$box->description}">
			Tooltip
		</span>
	</p>


	<div class="f-filter__inner">
		<ul class="f-filter__list">
			{foreach $box->getItems() as $value}

				{capture $link}{link 'this', filter => $value->followingFilterParameters, 'pager-page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}

				<li class="f-filter__item{if isset($box->visibleValuesCount) && $box->visibleValuesCount && $iterator->getCounter() > $box->visibleValuesCount} u-js-hide{/if}"{if isset($box->visibleValuesCount) && $box->visibleValuesCount && $iterator->getCounter() > $box->visibleValuesCount} data-filtergroup-target="hidden"{/if}>
					{if $box->getName() == 'color'}
						<label class="inp-item inp-item--checkbox" style="background-color: {$value->entity->alias}" aria-label="{$value->entity->value}">
							<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" class="inp-item__inp" {if !$value->isChecked && $value->count == 0} disabled{/if}{if $value->isChecked} checked{/if}>
							<span class="inp-item__text">
								{if !$value->isChecked && $value->count}
									({if isset($box->isPlus) && $box->isPlus}+{/if}{$value->count})
								{/if}
							</span>
						</label>
					{else}
						<label class="inp-item inp-item--checkbox">
							<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" data-action="change->FilterGroup#submitForm" class="inp-item__inp" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked{/if}>
							<span class="inp-item__text">


								{if !$value->isChecked && $value->count == 0}
									{$value->name}
									{if $box->unit}
										{$box->unit}
									{/if}
								{else}
									<a href="{$link}"{if $linkSeo->hasNofollow($object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if}>
										{$value->name}
										{if $box->unit}
											{$box->unit}
										{/if}
									</a>
								{/if}


								{if !$value->isChecked && $value->count}
									<span class="inp-item__count">
										({if isset($box->isPlus) && $box->isPlus}+{/if}{$value->count})
									</span>
								{/if}
							</span>
						</label>
					{/if}
				</li>
			{/foreach}
		</ul>

		{if $box->showMoreButton}
			<p>
				<button type="button" class="btn f-filter__more" data-action="filter-group#toggleHidden">
					{_btn_more_values}
				</button>
			</p>
		{/if}
	</div>
</div>
