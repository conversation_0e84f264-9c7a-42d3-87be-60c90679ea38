<?php declare(strict_types = 1);

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\MutationAgreement;
use App\Model\Orm;
use App\Model\Subscription;
use App\Model\SubscriptionModel;
use App\Model\SubscriptionOrder;
use App\Model\SubscriptionOrderAddress;
use App\Model\SubscriptionOrderModel;
use App\Model\SubscriptionPayment;
use App\Model\Tree;
use App\Model\User;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Forms\Control;
use Nette\Forms\Form;
use Nette\Http\Session;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use SuperKoderi\GoPay\GopayService;
use SuperKoderi\hasAgreementFormTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\ImageResizer;
use SuperKoderi\TranslatorDB;
use Throwable;
use Tracy;
use function array_column;

class SubscriptionActivationForm extends UI\Control
{

	use hasAgreementFormTrait;
	use hasMessageForFormComponentTrait;

	private Tree $object;

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		Session $session,
		private readonly SubscriptionModel $subscriptionModel,
		private Subscription $subscription,
		private readonly User $user,
		private readonly ImageResizer $imageResizer,
	)
	{
		$this->initAgreementItems($this->subscription->mutation, MutationAgreement::PLACE_SUBSCRIPTION_ACTIVATION, $this->subscription->cashOnDelivery ? [MutationAgreement::TYPE_SUBSCRIPTION_PAYMENT] : [MutationAgreement::TYPE_SUBSCRIPTION_CASH_ON_DELIVERY]);
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->subscription = $this->subscription;
		$this->template->userEntity = $this->subscription->user;
		$this->template->imageResizer = $this->imageResizer;
		$this->setAgreementTemplate();
		$this->template->render(__DIR__ . '/subscriptionActivationForm.latte');
	}

	/**
	 * Edit form factory.
	 */
	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$states = $this->subscription->mutation->getStates();

		$form->addSelect('state', 'state', $states)->setRequired()->setDefaultValue($this->subscription->mutation->countryDefault);

		$form->addText('email', 'email')->setHtmlAttribute('readonly', 'readonly')->setRequired();
		$form->addText('firstname', 'name')->setRequired();
		$form->addText('lastname', 'surname')->setRequired();
		$form->addText('phone', 'phone')->setRequired();
		$form->addText('street', 'street')->setRequired();
		$form->addText('city', 'city')->setRequired()->setDefaultValue('');
		$form->addInteger('zip', 'zip')->setRequired();

		$form->addCheckbox('deliveryTab', 'is_address_different');
		// phpcs:ignore
		/** @var Control $deliveryTab */
		$deliveryTab = $form['deliveryTab'];

		$form->addText('ic', 'ic');
		$form->addText('dic', 'dic');
		$form->addText('company', 'company');

		$form->addText('dFirstname', 'name')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dLastname', 'surname')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dCompany', 'company');

		$form->addText('dStreet', 'street')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dCity', 'city')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addText('dZip', 'zip')
			->addConditionOn($deliveryTab, Form::FILLED)
			->setRequired();

		$form->addSelect('dState', 'state', $states)->setDefaultValue($this->subscription->mutation->countryDefault);
		$form->addText('dInfo', 'info');
		$form->addText('dPhone', 'phone');

		$form->addTextArea('infotext', 'note');

		$this->addAgreementFormItems($form, $this->subscription->cashOnDelivery ? [MutationAgreement::TYPE_SUBSCRIPTION_PAYMENT] : [MutationAgreement::TYPE_SUBSCRIPTION_CASH_ON_DELIVERY]);

		$form->addSubmit('save', 'btn_subscription_reactivate');

		$data = [
				'firstname' => $this->subscription->firstname,
				'lastname' => $this->subscription->lastname,
				'email' => $this->subscription->email,
				'phone' => $this->subscription->phone,
				'zip' => $this->subscription->zip,
				'city' => $testingCity ?? $this->subscription->city,
				'street' => $this->subscription->street,
				'state' => $this->subscription->state !== '' ? $this->subscription->state : $this->subscription->mutation->countryDefault,
				'dStreet' => $this->subscription->dStreet,
				'dCity' => $this->subscription->dCity,
				'dInfo' => $this->subscription->dInfo,
				'dZip' => $this->subscription->dZip,
				'dPhone' => $this->subscription->dPhone,
				'dState' => $this->subscription->dState !== '' ? $this->subscription->dState : $this->subscription->mutation->countryDefault,
				'dCompany' => $this->subscription->dCompany,
				'ic' => $this->subscription->ic,
				'dic' => $this->subscription->dic,
				'company' => $this->subscription->company,
			];

		$form->setDefaults($data);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$valuesAll = $form->getHttpData();
			$payment = null;

			if (!$values->deliveryTab) {
				unset($valuesAll['dStreet']);
				unset($valuesAll['dName']);
				unset($valuesAll['dCity']);
				unset($valuesAll['dInfo']);
				unset($valuesAll['dZip']);
				unset($valuesAll['dState']);
				unset($valuesAll['dCompany']);
				unset($valuesAll['dPhone']);
			}

			$this->subscriptionModel->editSubscription($this->subscription, $this->subscription->name, $this->subscription->interval, userAddressData: $valuesAll);

			$order = $this->subscriptionModel->reactivateSubscription($this->subscription);

			if ($this->subscription->cashOnDelivery === false)  {
				$payment = $this->subscriptionModel->activateRecurringPayment($this->subscription, $order);
			}

		} catch (Throwable $e) {
			$this->flashMessage('Error', 'error');
			Tracy\Debugger::log($e, Tracy\ILogger::ERROR);
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			if ($payment instanceof SubscriptionPayment) {
				$this->presenter->redirectUrl($payment->paymentUrl);
			} else {
				$this->presenter->redirect('UID|subscriptionDetail', ['hash' => $this->subscription->hash]);
			}
		}
	}


}

interface ISubscriptionActivationFormFactory
{

	public function create(Subscription $subscription, User $user): SubscriptionActivationForm;

}
