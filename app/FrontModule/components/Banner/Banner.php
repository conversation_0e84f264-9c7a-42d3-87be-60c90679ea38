<?php


namespace SuperKoderi;

use App\Model\BannerModel;
use App\Model\Orm;
use App\Model\TreeModel;
use Nette\Application\UI;
use Nette\Caching\Cache;
use Nette\Caching\IStorage;
use Nextras\Orm\Entity\IEntity;

class Banner extends UI\Control
{
	private $translator;

	private $response;

	private $object;

	/** @var BannerModel */
	private $bannerModel;

	/** @var Orm */
	private $orm;

	/** @var ConfigService */
	private $configService;
	/**
	 * @var ImageResizer
	 */
	private $imageResizer;
	private $cache;

	public function __construct(IEntity $object = null,
	                            TranslatorDB $translator, \Nette\Http\Response $response, BannerModel $bannerModel, Orm $orm,
	                            ConfigService $configService, ImageResizer $imageResizer, IStorage $cacheStorage)
	{
		$this->cache = new \Nette\Caching\Cache($cacheStorage);
		$this->object = $object;
		$this->translator = $translator;
		$this->response = $response;
		$this->bannerModel = $bannerModel;
		$this->orm = $orm;
		$this->configService = $configService;
		$this->imageResizer = $imageResizer;
	}

	public function __call($name, $args)
	{
		if (preg_match('/^render(.+)/', $name, $matches)) {
			$position = \Nette\Utils\Strings::firstLower($matches[1]);
			if ($this->configService->get('banners', 'positions', $position) && $this->object) {

				$cacheKey = $position . '-' . $this->object->id;
				$possibleBannerIds = $this->cache->load($cacheKey);

				if ($possibleBannerIds === null) {
					$possibleBannerIds = $this->bannerModel->findBannersForPosition($this->object, $position)->fetchPairs(null, 'id');
					$this->cache->save($cacheKey, $possibleBannerIds,[
							Cache::TAGS => ['trees', 'banners']
					]);
				}

				if ($possibleBannerIds) {
					$randomKeyId = array_rand($possibleBannerIds, 1);
					$this->template->banner = $this->orm->banner->getById($possibleBannerIds[$randomKeyId]);
					$this->template->position = $position;
					$this->renderTemplate($position);
				}
			}
		}
	}

	private function renderTemplate($position = '')
	{

		$this->template->imageResizer = $this->imageResizer;
		$this->template->setTranslator($this->translator);
		$file = __DIR__ . '/' . $position . '.latte';
		if (file_exists($file)) {
			$this->template->render($file);
		} else {
			$this->template->render(__DIR__ . '/banner.latte');
		}
	}


	public function handleCLick($bannerId)
	{
		$banner = $this->orm->banner->getById($bannerId);
		if ($banner && $banner->link) {
			$this->response->redirect($banner->link);
		} else {
			$this->presenter->redirect('this');
		}
	}
}


interface IBannerFactory
{
	/**
	 * @return Banner
	 */
	function create(IEntity $object = null);
}
