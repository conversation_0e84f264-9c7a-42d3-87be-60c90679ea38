<?php declare(strict_types = 1);

namespace App\Model\CustomField;

use JsonSerializable;

final class SuggestUrl implements JsonSerializable
{

	/**
	 * @param array<string, mixed> $params
	 */
	public function __construct(
		public string $searchParameterName,
		public string $link,
		public array $params = [],
	)
	{
	}

	public function toArray(): array
	{
		return [
			'searchParameterName' => $this->searchParameterName,
			'link' => $this->link,
			'params' => $this->params,
		];
	}

	public function jsonSerialize(): array
	{
		return $this->toArray();
	}

}
