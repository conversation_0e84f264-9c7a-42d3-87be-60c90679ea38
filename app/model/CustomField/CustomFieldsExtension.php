<?php declare(strict_types = 1);

namespace App\Model\CustomField;

use Nette\DI\CompilerExtension;
use Nette\DI\Definitions\Reference;
use Nette\DI\Definitions\Statement;
use Nette\Schema\Expect;
use Nette\Schema\Schema;
use function array_key_exists;
use function array_map;
use function assert;
use function in_array;
use function is_array;
use function is_int;
use function is_string;
use function str_starts_with;
use function substr;

/**
 * @property-read array{definitions: array<string, array<string, mixed>>, fields: array<string, string|array<string, mixed>>, suggestUrls: array<string, array{searchParameterName: string, link: string, params:? mixed[]}>, templates: array<string, array<int|string, string|array<string, mixed>>>} $config
 */
final class CustomFieldsExtension extends CompilerExtension
{

	public static function getDefinitionSchema(): Schema
	{
		return Expect::anyOf(
			Expect::structure([
				'type' => Expect::string()->required(),
				'label' => Expect::string(),
			])->otherItems()->castTo('array'),
			Expect::structure([
				'extends' => Expect::string()->required()->pattern('@.+'),
				'label' => Expect::string(),
			])->otherItems()->castTo('array'),
		);
	}

	public function getConfigSchema(): Schema
	{
		$definitionSchema = self::getDefinitionSchema();

		return Expect::structure([
			'definitions' => Expect::arrayOf(
				$definitionSchema,
				keyType: 'string',
			),
			'fields' => Expect::arrayOf(
				Expect::anyOf(
					$definitionSchema,
					Expect::string()->pattern('@.+'),
				),
				keyType: 'string',
			),
			'suggestUrls' => Expect::arrayOf(
				Expect::structure([
					'searchParameterName' => Expect::string()->required(),
					'link' => Expect::string()->required(),
					'params' => Expect::array(),
				])->castTo('array'),
				keyType: 'string',
			),
			'templates' => Expect::arrayOf(
				Expect::arrayOf(
					Expect::anyOf(
						$definitionSchema,
						Expect::string()->pattern('@.+'),
					),
				)->required()->min(1),
				keyType: 'string',
			),
		])->castTo('array');
	}

	public function loadConfiguration(): void
	{
		$builder = $this->getContainerBuilder();

		foreach ($this->config['definitions'] as $name => $definition) {
			$builder->addDefinition($this->prefix("definitions.$name"))
				->setType(CustomFieldDefinition::class)
				->setFactory($this->processDefinition($definition))
				->setAutowired(false);
		}

		$suggestUrls = [];
		foreach ($this->config['suggestUrls'] as $name => $url) {
			$suggestUrl = $builder->addDefinition($this->prefix("suggestUrls.$name"))
				->setType(SuggestUrl::class)
				->setFactory(SuggestUrl::class, $url)
				->addTag(SuggestUrl::class, $name)
				->setAutowired(false);

			$suggestUrls[$name] = $suggestUrl;
		}

		$builder->addDefinition($this->prefix('suggestUrls'))
			->setFactory(SuggestUrls::class, [$suggestUrls]);

		foreach ($this->config['fields'] as $name => $definition) {
			$builder->addDefinition($this->prefix("fields.$name"))
				->setType(CustomField::class)
				->setFactory(CustomField::class, [
					$name,
					$this->processDefinition($definition),
				])
				->addTag(CustomField::class, $name)
				->setAutowired(false);

			$builder->addAlias($this->prefix($name), $this->prefix("fields.$name"));
		}

		$templates = array_map(
			function (array $customFields): array {
				$resolvedFields = [];
				foreach ($customFields as $name => $definition) {
					if (is_int($name)) {
						assert(is_string($definition));
						$resolvedFields[] = new Reference(substr($definition, 1));
					} else {
						$resolvedFields[] = new Statement(CustomField::class, [
							$name,
							$this->processDefinition($definition),
						]);
					}
				}

				return $resolvedFields;
			},
			$this->config['templates'],
		);

		$builder->addDefinition($this->prefix('customFields'))
			->setType(CustomFields::class)
			->setFactory(CustomFields::class, [$templates]);
	}

	/**
	 * @param string|array<string, mixed> $definition
	 */
	public function processDefinition(string|array $definition): Reference|Statement
	{
		if (is_string($definition)) {
			return new Reference(substr($definition, 1));
		}

		// resolve "url" in "suggest" to service reference or inline definition
		if (array_key_exists('type', $definition) && $definition['type'] === 'suggest') {
			if (is_string($definition['url']) && str_starts_with($definition['url'], '@')) {
				$definition['url'] = new Reference(substr($definition['url'], 1));
			} elseif (is_array($definition['url'])) {
				$definition['url'] = new Statement(SuggestUrl::class, $definition['url']);
			}
		}

		// recursively resolve items in "list" and "group" to service reference or inline definition
		if (array_key_exists('type', $definition)
			&& in_array($definition['type'], ['list', 'group'], true)
			&& array_key_exists('items', $definition)
		) {
			$definition['items'] = array_map(
				[$this, 'processDefinition'],
				$definition['items'],
			);
		}

		if (array_key_exists('extends', $definition)) {
			$extends = new Reference(substr($definition['extends'], 1));
			unset($definition['extends']);

			return new Statement(CustomFieldDefinition::class . '::extend', [
				$extends,
				$definition,
			]);
		}

		return new Statement(CustomFieldDefinition::class, [$definition]);
	}

}
