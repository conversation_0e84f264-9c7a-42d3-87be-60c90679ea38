<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\Seolink;
use App\Model\Tree;
use SuperKoderi\FilterCache;
use SuperKoderi\IFilterFactory;

class SeoLinkData implements Convertor
{
	const TYPE = 'seolink';

	public function convert(object $object): array
	{
		assert($object instanceof Seolink);
		$content = ($object->description !== null) ? $object->description : '';

//		$subType = $object->type;
//		if ($object->template == 'Brand:detail') {
//			$subType = 'brand';
//		}

		$public = $object->isActive == 1 && $object->noIndex !== 1;

//		if (isset($object->hasLinkedCategories) && $object->hasLinkedCategories) {
//			$public = false;
//		}

		//$filter = $this->filterFactory->create($object->mutation);
		//$this->filterCache->decoratePost()
		$publicFrom = (new \DateTimeImmutable())->sub(new \DateInterval('P1Y'));
		$publicTo = (new \DateTimeImmutable())->add(new \DateInterval('P99Y'));
		return [
			'id' => $object->id,
			'public' => $public,
			'name' => $object->name,
			'nameTitle' => $object->metaTitle,
			'nameAnchor' => $object->h1,
			'content' => strip_tags($content),
			'type' => self::TYPE,
//			'subType' => $subType,
			'hide' => (bool) $object->noIndex,
			'annotation' => '',
			'publicFrom' => ConvertorHelper::convertTime($publicFrom),
			'publicTo' => ConvertorHelper::convertTime($publicTo),
		];
	}
}
