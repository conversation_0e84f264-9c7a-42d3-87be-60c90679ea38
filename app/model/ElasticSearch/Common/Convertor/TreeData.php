<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\Tree;

class TreeData implements Convertor
{
	const TYPE = 'tree';
	const TYPE_BLOG = 'blog';

	public function convert(object $object): array
	{
		assert($object instanceof Tree);
		$content = ($object->content !== null) ? $object->content : '';

//		$subType = $object->type;
//		if ($object->template == 'Brand:detail') {
//			$subType = 'brand';
//		}

		$public = (bool)$object->public;

//		if (isset($object->hasLinkedCategories) && $object->hasLinkedCategories) {
//			$public = false;
//		}



		return [
			'id' => $object->id,
			'public' => $public,
			'name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => strip_tags($content),
			'type' => $object->isBlog ? self::TYPE_BLOG : self::TYPE,
//			'subType' => $subType,
			'hide' => (bool) $object->hideInSearch,
			'annotation' => $object->annotation,
			'publicFrom' => ConvertorHelper::convertTime($object->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($object->publicTo),
		];
	}
}
