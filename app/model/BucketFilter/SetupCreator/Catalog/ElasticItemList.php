<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\ElasticItem\Range;
use App\Model\BucketFilter\ElasticItem\SingleValue;
use App\Model\BucketFilter\QueryAggregation;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\Mutation;
use App\Model\Parameter;
use App\Model\ParameterValueFilterHelper;
use App\Model\Tree;
use App\Model\UserModel;

class ElasticItemList implements ElasticItemListGenerator
{

	private ?array $list;

	public function __construct(
		private readonly Tree $parameterObject,
		private readonly array $allSelectedParameters,
		private readonly Mutation $mutation,
		private readonly CatalogParameter $catalogParameter,
		private readonly QueryFilter $queryFilter,
		private readonly ParameterValueFilterHelper $parameterValueFilterHelper,
		private readonly UserModel $userModel,
	)
	{
	}



	public function getElasticItemList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getItemsForCatalog();
		}

		return $this->list;
	}


	private function getItemsForCatalog(): array
	{
		$filterItems = [];

//		$name = 'price';
//		$priceItem = new Range(
//			$this->queryFilter,
//			$name,
//			(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'] : null,
//			(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'] : null,
//		);
//		$priceItem->setElasticPath(['statePricesWithVat', $this->currentState->code, $this->priceLevel->type]);
//		$filterItems[] = $priceItem;

//		$name = 'isInStore';
//		$filterItems[] = new SingleValue(
//			$name,
//			$this->allSelectedParameters[DiscreteValues::NAMESPACE_FLAGS][$name] ?? [],
//			$this->queryFilter,
//			fn($value) => (bool) $value,
//		);


//		$name = 'isNew';
//		$filterItems[] = new SingleValue(
//			$name,
//			$this->allSelectedParameters[DiscreteValues::NAMESPACE_FLAGS][$name] ?? [],
//			$this->queryFilter,
//			fn($value) => (bool) $value,
//		);

		// TODO -
		$cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterObject);

		$numericAsSliderIds = [];
		if ($cfSetup && isset($cfSetup->visibleParameters)) {
			foreach ($cfSetup->visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter) && $visibleParameter->parameter->getEntity()) {
					if (isset($visibleParameter->numberAsRange) && $visibleParameter->numberAsRange) {
						$numericAsSliderIds[] = $visibleParameter->parameter->id;
					}
				}
			}
		}

		$esParameters = $this->catalogParameter->getPossibleParametersForCatalog($this->parameterObject);
		foreach ($esParameters as $esParameter) {
			if ($esParameter->type === Parameter::TYPE_MULTISELECT) {

				$selectedValues = $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [];

				$multiValue = new MultiValue(
					$esParameter->uid,
					$selectedValues,
					$this->parameterValueFilterHelper->getFunctionForValues($esParameter),
					$this->queryFilter,
					fn($value) => (int)$value,
				);

//				if ($esParameter->uid === Parameter::UID_VIRTUAL_FLAG) {
//					//$gKeys = $this->userModel->getGroupKey($this->mutation);
//					//$multiValue->setElasticPath(['tags', $gKeys]);
//				}

				if ($esParameter->uid === Parameter::UID_PROTEIN) {
					$selectedValues = $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [];
//					bd($selectedValues);

					$proteinValues = $esParameter->options->toCollection()->fetchPairs('id', 'id');
					foreach ($proteinValues as $paramValueId => $paramValue) {
						if (in_array($paramValue, $selectedValues)) {
							unset($proteinValues[$paramValueId]);
						}
					}
//					bd("setSelectedValueOverwrite");
//					bd($proteinValues);
					$multiValue->setSelectedValueOverwrite($proteinValues);
				}



				$filterItems[] = $multiValue;
			} elseif ($esParameter->type === Parameter::TYPE_SELECT) {
				$filterItems[] = new SingleValue(
					$esParameter->uid,
					$this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [],
					$this->queryFilter,
				);
			} elseif ($esParameter->type === Parameter::TYPE_NUMBER) {
				if (in_array($esParameter->id, $numericAsSliderIds)) {
					// add range for numbers
					$filterItems[] = new Range(
						$this->queryFilter,
						$esParameter->uid,
						(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['min'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['min'] : null,
						(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['max'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['max'] : null,
					);
				} else {
					$filterItems[] = new SingleValue(
						$esParameter->uid,
						$this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [],
						$this->queryFilter,
					);
				}
			}
		}

		return $filterItems;
	}

}
