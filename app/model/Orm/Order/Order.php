<?php

/** @noinspection PhpUnused */

namespace App\Model;

use App\Model\Document\DocumentType;
use App\Model\Orm\Document\Document;
use App\Model\Orm\TraitsEntity\HasCache;
use App\Model\Orm\TraitsEntity\HasFormDefaultData;
use http\Exception\RuntimeException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use Nextras\Orm\Relationships\OneHasOne;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasConstsTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\MoneyHelper;
use function array_map;
use function array_merge;
use function array_sum;
use function count;
use function in_array;
use function ksort;
use function max;
use function round;

/**
 * @property int $id {primary}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_NEW}
 * @property string|null $transportType {enum self::TRANSPORT_*} {default self::TRANSPORT_PERSONAL}
 * @property string|null $paymentType {enum self::PAYMENT_*} {default self::PAYMENT_CASH}
 * @property string|null $subPaymentType {enum self::ONLINE_PAYMENT_TYPE*}
 * @property string $number {default ''}
 * @property DateTimeImmutable $created {default 'now'}
 * @property int|null $zasilkovnaId
 * @property string|null $transportInfoText
 * @property string|null $pplParcelCode
 * @property array $transportData {wrapper JsonContainer}
 * @property string $email {default ''}
 * @property string $firstname {default ''}
 * @property string $lastname {default ''}
 * @property string $phone {default ''}
 * @property string $street {default ''}
 * @property string $city {default ''}
 * @property string $zip {default ''}
 * @property string $state {default ''}
 * @property string $company {default ''}
 * @property string $ic {default ''}
 * @property string $dic {default ''}
 * @property string $dName {default ''}
 * @property string $dFirstname {default ''}
 * @property string $dLastname {default ''}
 * @property string $dCompany {default ''}
 * @property string $dPhone {default ''}
 * @property string $dStreet {default ''}
 * @property string $dCity {default ''}
 * @property string $dZip {default ''}
 * @property string $dState {default ''}
 * @property string $dInfo {default ''}
 * @property string $infotext {default ''}
 * @property DateTimeImmutable|null $invoiceDate
 * @property string|null $invoiceNumber
 * @property DateTimeImmutable|null $mailConfirmationSend
 * @property DateTimeImmutable|null $mailPrepared
 * @property DateTimeImmutable|null $mailSent
 * @property DateTimeImmutable|null $mailStorno
 * @property DateTimeImmutable|null $mailStornoWarehouse
 * @property DateTimeImmutable|null $mailProgress
 * @property DateTimeImmutable|null $mailUnpaidOrderSent
 * @property string $barCode {default ''}
 * @property int $statsRead {default 0}
 * @property int|null $enableHeurekaQuery {default 0}
 * @property string $pinInsurance {default ''}
 * @property int $isDeleted {default 0}
 * @property DateTimeImmutable|null $mailChimped
 * @property string|null $cancelReason
 * @property array $invoiceData {wrapper JsonContainer}
 * @property DateTimeImmutable|null $doneDate
 * @property DateTimeImmutable|null $canceled
 * @property string|null $deliveryInfoUrl
 *
 * PLATEBNI BRANA
 * @property int|null $paymentId
 * @property string|null $paymentJson
 * @property string|null $paymentStatus {enum self::ONLINE_PAYMENT_*}
 * @property string|null $paymentHistory
 *
 * Noviko
 * @property int|null $novikoId
 * @property int|null $novikoHdId
 * @property int|null $novikoStatus
 * @property DateTimeImmutable|null $novikoUpdated
 * @property array $parcelNumber {wrapper JsonContainer}
 * @property-read string $novikoIdObjednavkaPart {virtual} ID, ktere se posila do Novika jako parovaci mezi eshop <-> Noviko
 * @property-read string $novikoStatusName {virtual}
 * @property-read bool $isCanNovikoSaveHeader {virtual}
 *
 *
 * RELATIONS
 * @property OrderItem[]|OneHasMany $items {1:m OrderItem::$order}
 * @property SubscriptionOrder|null $subscriptionOrder {1:1 SubscriptionOrder::$order}
 * @property CreditNote[]|OneHasMany $creditNotes {1:m CreditNote::$order}
 * @property User|null $user {m:1 User::$orders}
 * @property Mutation $mutation {m:1 Mutation::$orders} {default 1}
 * @property Document[]|OneHasMany $documents {1:m Document::$order}
 * @property VoucherCodeUses[]|OneHasMany $voucherCodeUses {1:m VoucherCodeUses::$order}
 *
 *
 * VIRTUAL
 * @property-read Document|null $invoiceDocument {virtual}
 * @property-read OrderItem[]|ICollection $products {virtual}
 * @property-read OrderItem[]|ICollection $subscriptions {virtual}
 * @property-read bool $isSubscriptionOrder {virtual}
 * @property-read OrderItem[]|ICollection $allPayedProducts {virtual}
 * @property-read OrderItem[]|ICollection $parentItems {virtual}
 * @property-read OrderItem[]|ICollection $presents {virtual}
 * @property-read OrderItem[]|ICollection $samples {virtual}
 * @property-read OrderItem[]|ICollection $giftExtra {virtual}
 * @property-read OrderItem[]|ICollection $services {virtual}
 * @property-read OrderItem[]|ICollection $warranties {virtual}
 * @property-read OrderItem[]|ICollection $insurances {virtual}
 * @property-read OrderItem[]|ICollection $rents {virtual}
 * @property-read OrderItem|null $transport {virtual}
 * @property-read OrderItem|null $payment {virtual}
 * @property-read OrderItem[]|ICollection $vouchers {virtual}
 * @property-read OrderItem[]|ICollection $discounts {virtual}
 * @property-read float $totalPriceItemOnlyDPH {virtual}
 * @property-read float $totalSubscriptionPriceItemOnlyDPH {virtual}
 * @property-read float $totalOneTimePriceItemOnlyDPH {virtual}
 * @property-read float $totalPriceDPH {virtual}
 * @property-read float $totalPrice {virtual}
 * @property-read float $revenueGTM {virtual}
 * @property-read float $p {virtual}
 * @property-read bool $possibleFreeTransportPrice {virtual}
 * @property-read float $productsPriceDPH {virtual}
 * @property-read bool $transportCanBeFree {virtual}
 * @property-read string $paymentName {virtual}
 * @property-read bool $hasDeliveryAddress {virtual}
 * @property-read bool $isCanStorno {virtual}
 * @property-read bool $isCanRevokeStorno {virtual}
 * @property bool $productMissing {virtual} {default false}
 * @property array $uniqueKeyErrors {virtual} {default []}
 * @property-read  string $name {virtual}
 * @property-read  string $hash {virtual}
 * @property-read  bool $showRetryPayment {virtual}
 * @property-read  bool $isOnlinePayment {virtual}
 * @property-read  bool $emailConfirmationFailed {virtual}
 *
 * @property-read Subscription|null $subscription {virtual}
 *
 * @property-read  OrderItem[] $itemsForInvoice {virtual}
 * @property-read  OrderItem[] $productsForInvoice {virtual}
 * @property-read  bool $hasInvoice {virtual}
 * @property-read  string $invoicePdfFileName {virtual}
 * @property-read  string $invoiceXMLFileName {virtual}
 * @property-read  DateTimeImmutable|null $dueDate {virtual}
 * @property-read  ArrayHash $invoicePriceOverview {virtual}
 * @property-read  ArrayHash $invoiceVatSummary {virtual}
 * @property-read  float $roundingForPrice {virtual}
 * @property-read  float $cashPaymentRounding {virtual}
 *
 * @property-read  array|OrderItem[] $itemsForCreditNote {virtual}
 * @property-read bool $canCreateCreditNote {virtual}
 * @property-read  bool $hasCreditNote {virtual}
 * @property-read  array $creditNotesPossibleItemsAmount {virtual}
 * @property-read  int $creditNotesPossibleItemsAmountSum {virtual}
 * @property-read  string|null $voucherCodeInOrder {virtual}
 * @property-read  int $weight {virtual}
 *
 * @property-read bool $readyToCloseInNoviko {virtual}
 *
 * PLATEBNI BRANA
 * @property-read bool $isPayed {virtual}
 *
 * @property-read bool $hasTrackingPackageNumber {virtual}
 */
class Order extends Entity
{

	use HasFormDefaultData;
	use hasConfigServiceTrait;
	use HasCache;
	use hasConstsTrait;
	use hasOrmTrait;

	public const ONLINE_PAYMENT_CREATED = 'CREATED';  // Platba založena
	public const ONLINE_PAYMENT_PAYMENT_METHOD_CHOSEN = 'PAYMENT_METHOD_CHOSEN'; //Platební metoda vybrána
	public const ONLINE_PAYMENT_PAID = 'PAID'; // Platba zaplacena
	public const ONLINE_PAYMENT_AUTHORIZED = 'AUTHORIZED'; // Platba předautorizována
	public const ONLINE_PAYMENT_CANCELED = 'CANCELED'; // Platba zrušena
	public const ONLINE_PAYMENT_TIMEOUTED = 'TIMEOUTED'; // Vypršelá platnost platby
	public const ONLINE_PAYMENT_REFUNDED = 'REFUNDED'; // refundována
	public const ONLINE_PAYMENT_PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'; // refundována
	public const ONLINE_PAYMENT_ERROR = 'error';
	public const ONLINE_PAYMENT_PAID_NEXT_ATTEMPT = 'PAID_NEXT_ATTEMPT'; // zaplacena na druhý pokus (Oni se totiž telefonicky s klientem domluví na realizaci objednávky. Přes admin prostředí GP Webpay mu pošlou odkaz na online platbu. Klient to tedy zaplatí)

	public const STATUS_NEW = 'new'; // nová
	public const STATUS_WAITING = 'waiting'; // ceka na zapalcnei
	public const STATUS_PROGRESS = 'progress'; // zpracovává se
	public const STATUS_SHIPPED = 'shipped'; // expedována
	public const STATUS_DONE = 'done'; // vyřízená
	public const STATUS_CANCEL = 'cancel'; // stornována
	public const STATUS_RETURNED_BACK = 'returned'; // vráceno dopravcem

	public const TRANSPORT_PERSONAL = 'personal';
	public const TRANSPORT_DPD = 'dpd';
	public const TRANSPORT_DPDSK = 'dpdSk';
	public const TRANSPORT_DPDEUROPE = 'dpdEurope';
	public const TRANSPORT_POST = 'post';
	public const TRANSPORT_POSTERESTANTE = 'posteRestante';
	public const TRANSPORT_ZASILKOVNA = 'zasilkovna';
	public const TRANSPORT_PERSONAL_SPECIAL = 'personalSpecial';
	public const TRANSPORT_PPL_PARCEL = 'ppl_parcel';
	public const TRANSPORT_PPL = 'ppl';

	public const PAYMENT_CASH = 'cash';
	public const PAYMENT_ONDELIVERY = 'onDelivery';
	public const PAYMENT_BANK = 'bank';
	public const PAYMENT_ONLINE = 'online';
	public const PAYMENT_INSTALLMENT = 'installment';

	public const ONLINE_PAYMENT_TYPE_CARD = 'online';
	public const ONLINE_PAYMENT_TYPE_APPLE_PAY = 'onlineApplePay';
	public const ONLINE_PAYMENT_TYPE_GOOGLE_PAY = 'onlineGooglePay';
	public const ONLINE_PAYMENT_TYPE_BANK_ACCOUNT = 'onlineBankAccount';

	public const ALL_ONLINE_PAYMENT_TYPES = [
		self::ONLINE_PAYMENT_TYPE_CARD,
		self::ONLINE_PAYMENT_TYPE_APPLE_PAY,
		self::ONLINE_PAYMENT_TYPE_GOOGLE_PAY,
		self::ONLINE_PAYMENT_TYPE_BANK_ACCOUNT,
	];

	public const GOPAY_PAYMENT_INSTRUMENT = [
		self::ONLINE_PAYMENT_TYPE_CARD => 'PAYMENT_CARD',
		self::ONLINE_PAYMENT_TYPE_APPLE_PAY => 'APPLE_PAY',
		self::ONLINE_PAYMENT_TYPE_GOOGLE_PAY => 'GPAY',
		self::ONLINE_PAYMENT_TYPE_BANK_ACCOUNT => 'BANK_ACCOUNT',
	];

	// storno duvody !!!!! po pridani noveho duvodu je potreba pridat i odpovidajici email sablonu pro kazdou mutaci = kazdy duvod ma vlastni !!!!!!!
	public const CANCEL_REASON_DEFAULT = 'Default'; // stornovaná na žiadosť zákazníka
	public const CANCEL_REASON_RETURNED_PACKAGE = 'ReturnedPackage'; // neprevzatá zásielka
	public const CANCEL_REASON_LOST_PACKAGE = 'LostPackage'; // stratená zásielka
	public const CANCEL_REASON_DAMAGED_PACKAGE = 'DamagedPackage'; // poškozené zboží
	public const CANCEL_REASON_NOT_IN_STOCK = 'NotInStock'; // tovar nie je skladom
	public const CANCEL_REASON_PAYMENT_TIMED_OUT = 'PaymentTimedOut'; // vypršal čas na online platbu
	public const CANCEL_REASON_WITHDRAW_CONTRACT = 'WithdrawContract'; // odstúpenie od zmluvy
	public const CANCEL_REASON_COMPLAINT_GOODS = 'ComplaintGoods'; // reklamácia

	//@deprecated
	public function getProducts()
	{
		//TODO VOJTA deprecated
		return $this->getterProducts();
	}


	protected function getterProducts()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_PRODUCT]);
	}

	protected function getterSubscriptions()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_SUBSCRIPTION]);
	}

	protected function getterAllPayedProducts()
	{
		return $this->items->toCollection()->findBy(['type' => [OrderItem::TYPE_PRODUCT, OrderItem::TYPE_SUBSCRIPTION]])->orderBy('sort');
	}

	protected function getterVouchers()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_VOUCHER, 'parentId' => null]);
	}


	protected function getterDiscounts()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_DISCOUNT, 'parentId' => null]);
	}


	protected function getterTransport()
	{
		return $this->items->toCollection()->getBy(['type' => OrderItem::TYPE_TRANSPORT, 'parentId' => null]);
	}


	protected function getterPayment()
	{
		return $this->items->toCollection()->getBy(['type' => OrderItem::TYPE_PAYMENT, 'parentId' => null]);
	}


	protected function getterPresents()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_PRESENT, 'parentId' => null]);
	}


	protected function getterSamples()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_SAMPLE]);
	}


	protected function getterGiftExtra()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_PRESENT_EXTRA, 'parentId' => null]);
	}


	protected function getterServices()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_SERVICE]);
	}


	protected function getterWarranties()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_WARRANTY]);
	}

	protected function getterInsurances()
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_INSURANCE]);
	}


	protected function getterTotalPriceItemOnlyDPH(): float
	{
		$totalPriceDPH = 0;

		foreach ($this->items as $item) {
			if (!in_array($item->type, [
				OrderItem::TYPE_VOUCHER,
				OrderItem::TYPE_TRANSPORT,
				OrderItem::TYPE_PAYMENT,
				OrderItem::TYPE_CASH_ROUNDING
			], true)) {
				$totalPriceDPH += round($item->totalPriceDPH, $this->mutation->roundPositionsExtended);
			}
		}

		return $totalPriceDPH + $this->roundingForPrice;
	}

	protected function getterTotalSubscriptionPriceItemOnlyDPH(): float
	{
		$totalPriceDPH = 0;

		foreach ($this->subscriptions as $item) {
			if ($item->type === OrderItem::TYPE_SUBSCRIPTION) {
				$total = $item->unitPriceDPH * $item->amount;
				$totalPriceDPH += round($total, $this->mutation->roundPositionsExtended);
			}
		}

		return $totalPriceDPH + $this->roundingForPrice;
	}

	protected function getterTotalOneTimePriceItemOnlyDPH(): float
	{
		$totalPriceDPH = 0;

		foreach ($this->products as $item) {
			if ($item->type === OrderItem::TYPE_PRODUCT) {
				$total = $item->unitPriceDPH * $item->amount;
				$totalPriceDPH += round($total, $this->mutation->roundPositionsExtended);
			}
		}

		return $totalPriceDPH + $this->roundingForPrice;
	}


	protected function getterTotalPriceDPH(): float
	{
		$totalPriceDPH = 0;

		foreach ($this->items as $item) {
			$totalPriceDPH += round($item->totalPriceDPH, $this->mutation->roundPositionsExtended);
		}

		return $totalPriceDPH + $this->roundingForPrice;
	}


	protected function getterTotalPrice()
	{
		$totalPrice = 0;

		foreach ($this->items as $item) {
			$totalPrice += $item->totalPrice;
		}

		return $totalPrice;
	}


	protected function getterRoundingForPrice()
	{
		$priceOverview = $this->invoicePriceOverview;

		if ($this->mutation->roundPositionsExtended > 0) {
//			$price = round($priceOverview->priceDPH, $this->mutation->roundPositionsExtended);
			$rounding = 0;
		} else {
			$rounding = round(ceil($priceOverview->priceDPH) - $priceOverview->priceDPH, 2);
		}

		return $rounding;
	}


	protected function getterProductsPriceDPH()
	{
		$priceDPH = 0;
		foreach ($this->products as $product) {
			if ($product->variant) {
				$priceDPH = $priceDPH + $product->variant->priceFinalDPH * $product->amount;
			}
		}
		foreach ($this->subscriptions as $product) {
			if ($product->variant) {
				$priceDPH = $priceDPH + $product->variant->priceFinalDPH * $product->amount;
			}
		}

		return $priceDPH;
	}

	protected function getterPossibleFreeTransportPrice()
	{
		$transports = $this->mutation->transportsArray;

		if (!isset($this->cache['transportFreeFrom'])) {
			foreach ($transports as $transport) {

				if (!isset($this->cache['transportFreeFrom']) && $transport['freeFrom']) {
					// first iteration
					$this->cache['transportFreeFrom'] = $transport['freeFrom'];
				} else if (isset($this->cache['transportFreeFrom']) && $transport['freeFrom'] && ($transport['freeFrom'] < $this->cache['transportFreeFrom'])) {
					$this->cache['transportFreeFrom'] = $transport['freeFrom'];
				}
			}
		}

		return $this->cache['transportFreeFrom'];
	}


	protected function getterTransportCanBeFree()
	{
		if (!$this->possibleFreeTransportPrice) {
			return false;
		}
		return ($this->productsPriceDPH >= $this->possibleFreeTransportPrice);
	}


	protected function getterIsPayed()
	{
		return ($this->paymentType == self::PAYMENT_ONLINE &&
			($this->paymentStatus == self::ONLINE_PAYMENT_PAID || $this->paymentStatus == self::ONLINE_PAYMENT_PAID_NEXT_ATTEMPT));
	}


	protected function getterParentItems()
	{
		return $this->items->toCollection()->findBy(['parentId' => null, 'type' => [OrderItem::TYPE_PRODUCT, OrderItem::TYPE_SUBSCRIPTION]]);
	}


	public function getRentsCount()
	{
		$count = 0;
		foreach ($this->rents as $i) {
			$count += $i->amount;
		}
		return $count;
	}


	protected function getterRevenueGTM()
	{
		return $this->totalPriceDPH - $this->payment->totalPriceDPH - $this->transport->totalPriceDPH;
	}


	protected function getterPaymentName()
	{
		$payments = $this->mutation->paymentsArray;

		if (isset($payments[$this->paymentType]) && isset($payments[$this->paymentType]['name'])) {
			return $payments[$this->paymentType]['name'];
		} else {
			return 'unknown';
		}
	}

	protected function getterNovikoIdObjednavkaPart()
	{
		return $this->number;
	}

	protected function getterNovikoStatusName()
	{
		return isset(\SuperKoderi\Noviko\Order\Order::$statuses[$this->novikoStatus]) ? \SuperKoderi\Noviko\Order\Order::$statuses[$this->novikoStatus] : 'order_noviko_status_unknown';
	}

	protected function getterHasDeliveryAddress()
	{
		return !empty($this->dFirstname) && !empty($this->dLastname);
	}

	protected function getterIsCanNovikoSaveHeader()
	{
		return $this->novikoId && $this->status != self::STATUS_CANCEL && $this->novikoStatus <= \SuperKoderi\Noviko\Order\Order::ID_STATUS_DELAYING_DELIVERY;
	}

	protected function getterIsCanStorno()
	{
		return !in_array($this->status, [self::STATUS_CANCEL]);
	}

	protected function getterIsCanRevokeStorno()
	{
		return !$this->novikoId && $this->status == self::STATUS_CANCEL && $this->paymentType == self::PAYMENT_ONLINE;
	}

	protected function getterName()
	{
		return trim($this->firstname . ' ' . $this->lastname);
	}

	protected function getterHasInvoice()
	{
		return $this->invoiceDate !== null;
	}


	protected function getterInvoicePdfFileName()
	{
		return 'calibra_fa_' . $this->invoiceNumber . '.pdf';
	}


	protected function getterInvoiceXMLFileName()
	{
		return 'calibra_fa_' . $this->invoiceNumber . '.xml';
	}


	/** @return DateTimeImmutable|null */
	protected function getterDueDate()
	{
		if (!$this->invoiceDate) {
			return null;
		}

		$dueDate = clone $this->invoiceDate;
		$mutationDueDays = array_key_exists('dueDateNumber',  $this->mutation->invoiceData) ? $this->mutation->invoiceData['dueDateNumber'] : 14;
		$dueDateNumber = array_key_exists('dueDateNumber', $this->invoiceData) ? $this->invoiceData['dueDateNumber'] : $mutationDueDays;

		return $dueDate->modify(sprintf('+ %d day', $dueDateNumber));
	}


	protected function getterCanCreateCreditNote()
	{
		return $this->hasInvoice && $this->status != self::STATUS_CANCEL && $this->creditNotesPossibleItemsAmount;
	}

	protected function getterHasCreditNote()
	{
		return $this->creditNotes->count() > 0;
	}

	/**
	 * @return ArrayHash
	 *
	 *        price => 138.85
	 *        priceDPH => 168.0
	 *        dph => 29.16
	 */
	protected function getterInvoicePriceOverview(): ArrayHash
	{
		$price = new ArrayHash();
		$price->price = 0.0;
		$price->priceDPH = 0.0;
		$price->dph = 0.0;

		foreach ($this->itemsForInvoice as $item) {
			$pDph = $item->totalPriceDPH;
			$p = $item->totalPrice;
			$dph = $item->totalDPH;

			$price->priceDPH += round($pDph, $this->mutation->roundPositionsExtended);
			$price->price += round($p, $this->mutation->roundPositionsExtended);
			$price->dph += round($dph, $this->mutation->roundPositionsExtended);
		}

		return $price;
	}

	/**
	 * @return ArrayHash VAT recapitulation
	 */
	protected function getterInvoiceVatSummary(): ArrayHash
	{
		return $this->getItemsVatSummary($this->itemsForInvoice);
	}

	protected function getProductsVatSummary(): ArrayHash
	{
		return $this->getItemsVatSummary($this->productsForInvoice);
	}

	/**
	 * @param array<OrderItem> $items
	 * @return ArrayHash VAT recapitulation
	 */
	protected function getItemsVatSummary(array $items): ArrayHash
	{
		$summary = [];
		foreach ($items as $item) {
			$summary[$item->vat]['total'] ??= 0;
			$summary[$item->vat]['totalVat'] ??= 0;
			$summary[$item->vat]['sumVat'] ??= 0;

			// let's use better rounding precision
			$summary[$item->vat]['total'] += round($item->totalPrice, $this->mutation->roundPositionsExtended);
			$summary[$item->vat]['totalVat'] += round($item->totalPriceDPH, $this->mutation->roundPositionsExtended);
			$summary[$item->vat]['sumVat'] += round($item->totalDPH, $this->mutation->roundPositionsExtended);
		}

		ksort($summary);

		return ArrayHash::from($summary);
	}

	/**
	 * @return array - only products, subscriptions and gifs, used to compute voucher's VAT
	 */
	protected function getterProductsForInvoice(): array
	{
		if (!isset($this->cache['productsForInvoice'])) {
			$items = [];
			foreach ($this->items as $item) {
				if (!$item->isForInvoice || !in_array($item->type, [OrderItem::TYPE_PRODUCT, OrderItem::TYPE_PRESENT, OrderItem::TYPE_SUBSCRIPTION], true)) {
					continue;
				}

				$items[] = $item;
			}

			$this->cache['productsForInvoice'] = $items;
		}

		return $this->cache['productsForInvoice'];
	}

	/**
	 * @return OrderItem[]
	 */
	protected function getterItemsForInvoice(): array
	{
		if (!isset($this->cache['itemsForInvoice'])) {
			$items = [];
			foreach ($this->items as $item) {
				if (!$item->isForInvoice) {
					continue;
				}

				if ($item->type === OrderItem::TYPE_VOUCHER) {
					$items = array_merge($items, $this->explodeVoucher($item));
				} else {
					$items[] = $item;
				}
			}

			$this->cache['itemsForInvoice'] = $items;
		}

		return $this->cache['itemsForInvoice'];
	}

	/**
	 * Explodes voucher into vouchers for every VAT level, for correct VAT on invoice
	 *
	 * @param OrderItem $item - must be of type TYPE_VOUCHER
	 * @return OrderItem[]
	 */
	protected function explodeVoucher(OrderItem $item): array
	{
		if ($item->type !== OrderItem::TYPE_VOUCHER) {
			throw new RuntimeException('Invalid order item type - this works only for vouchers!');
		}

		$vatRecap = $this->getItemsVatSummary($this->productsForInvoice);

		if (count($vatRecap) === 1) {
			// pokud je jen jedno DPH - do voucheru vrátíme DPH podle produktů v košíku
			// (jinak tam zustane 21% výchozí dph)
			foreach ($vatRecap as $vatRate => $vatGroup) {
				$item->vat = $vatRate;
			}
			return [$item];
		}

		$total = array_sum(array_map(static fn ($item) => $item['total'], (array) $vatRecap));
		$priceVatLeft = round($item->totalPriceDPH, $this->mutation->roundPositions);
		$vouchers = [];

		foreach ($vatRecap as $vatRate => $vatGroup) {
			$coef = $vatGroup['total'] / $total;
			$priceVat = round($coef * $item->totalPriceDPH, $this->mutation->roundPositions);
			$priceVat = max($priceVat, $priceVatLeft); //we are in negative number, thus maximum
			$price = MoneyHelper::getPriceWithoutDPH(
				$priceVat,
				$vatRate,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($this->mutation, $this->mutation->roundPositionsExtended),
			);

			$priceVatLeft -= $priceVat;

			$orderItem = new OrderItem();
			//$orderItem->order = $item->order; //must not be attached to the order, or it will be renderend in the cart
			$orderItem->productId = 0;
			$orderItem->name = $item->name . ' (' . $vatRate . ' %)';
			$orderItem->unitPrice = $orderItem->originalUnitPrice = $price;
			$orderItem->unitPriceDPH = $orderItem->originalUnitPriceDPH = $priceVat;
			$orderItem->vat = $vatRate;
			$orderItem->amount = 1;
			$orderItem->type = $item->type;
			$orderItem->subType = $item->subType;
			$this->orm->orderItem->attach($orderItem);

			$vouchers[] = $orderItem;
		}

		if ($priceVatLeft < -0.009 && isset($vouchers[0])) {
			$firstItem = $vouchers[0];
			$firstItem->unitPriceDPH = round($firstItem->unitPriceDPH + $priceVatLeft, $this->mutation->roundPositions);
			$firstItem->originalUnitPriceDPH = $firstItem->unitPriceDPH;
			$firstItem->unitPrice = MoneyHelper::getPriceWithoutDPH(
				$firstItem->unitPriceDPH,
				$firstItem->vat,
				customPrecision: MoneyHelper::unitPriceRoundingPositions($this->mutation, $this->mutation->roundPositionsExtended),
			);

			$firstItem->originalUnitPrice = $firstItem->unitPrice;
		}

		return $vouchers;
	}

	/**
	 * @return array|OrderItem[]
	 */
	protected function getterItemsForCreditNote()
	{
		if (!isset($this->cache['itemsForCreditNote'])) {

			$items = [];
			foreach ($this->items as $item) {
				if ($item->isForCreditNote) {
					$items[] = $item;
				}
			}
			$this->cache['itemsForCreditNote'] = $items;
		}
		return $this->cache['itemsForCreditNote'];
	}

	/**
	 *
	 * Vrátí max možný počet položek, které lze ještě dobropisovat
	 *
	 *        orderItemId => amount
	 *        [148 => 3, 149 => 3]
	 *
	 * @return array
	 */
	protected function getterCreditNotesPossibleItemsAmount()
	{
		$items = [];

		foreach ($this->itemsForCreditNote as $item) {
			$items[$item->id] = $item->amount;
		}

		foreach ($this->creditNotes as $creditNote) {
			foreach ($creditNote->items as $creditNoteItem) {
				if (isset($items[$creditNoteItem->orderItemId])) {
					$items[$creditNoteItem->orderItemId] -= $creditNoteItem->amount;
				}
			}
		}
		return $items;
	}

	/**
	 * 0 = už není co k dobropisování
	 * @return int
	 */
	protected function getterCreditNotesPossibleItemsAmountSum()
	{
		return array_sum($this->creditNotesPossibleItemsAmount);
	}


	protected function getterVoucherCodeInOrder()
	{
		if ($this->vouchers->count()) {
			foreach ($this->vouchers as $v) {
				$code = $this->orm->voucherCode->getById($v->subType);
				if ($code) {
					return $code->code;
				}
			}
		}

		return null;
	}


	/**
	 * @return int
	 */
	protected function getterWeight(): int
	{
		$totalWeight = 0;
		foreach ($this->items as $item) {
			if (isset($item->variant->weight) && $item->variant->weight) {
				$totalWeight += ($item->variant->weight * $item->amount);
			}
		}
		return $totalWeight;
	}


	protected function getterCashPaymentRounding(): float
	{
		$rounding = 0;
		foreach ($this->items as $item) {
			if ($item->type == OrderItem::TYPE_CASH_ROUNDING) {
				$rounding = $item->totalPriceDPH;
			}
		}

		return $rounding;
	}

	protected function getterInvoiceDocument(): Document|null
	{
		return $this->documents->toCollection()->getBy([
			'order' => $this,
			'type' => DocumentType::INVOICE->value,
		]);
	}

	/**
	 * @return Document[]|ICollection
	 */
	protected function getterCreditNoteDocuments(): ICollection
	{
		return $this->documents->toCollection()->findBy([
			'type' => DocumentType::CREDIT_NOTE->value,
		]);
	}

	protected function getterHash(): string
	{
		return sha1($this->id . $this->number);
	}

	protected function getterShowRetryPayment(): bool
	{
		$show = false;

		if (
			$this->paymentType === self::PAYMENT_ONLINE &&
			$this->status === self::STATUS_WAITING &&
			in_array($this->paymentStatus, [null, self::ONLINE_PAYMENT_TIMEOUTED, self::ONLINE_PAYMENT_CREATED, self::ONLINE_PAYMENT_CANCELED, self::ONLINE_PAYMENT_ERROR, self::ONLINE_PAYMENT_PAYMENT_METHOD_CHOSEN], true))
		{
			$show = true;
		}

		return $show;
	}

	static public function isOnlinePayment(string $paymentType): bool
	{
		return in_array($paymentType, self::ALL_ONLINE_PAYMENT_TYPES, true);
	}

	protected function getterEmailConfirmationFailed(): bool
	{
		return ($this->status === self::STATUS_NEW) && ($this->mailConfirmationSend === null);
	}

	protected function getterSubscription(): Subscription|null
	{
		if ($this->subscriptionOrder) {
			return $this->subscriptionOrder->subscription;
		}

		return null;
	}

	protected function getterReadyToCloseInNoviko(): bool
	{
		// cloe order if it is in no subscription, set false if expedition date is in future or if it is cash on delivery and expedition date is in future - this is fix for first order
		$closeOrder = true;
		if ($this->subscriptionOrder instanceof SubscriptionOrder) {
			$closeOrder = false;
			if (
				($this->novikoId != null &&
				$this->subscriptionOrder->payOrderAfter < new DateTimeImmutable())
				|| ($this->subscriptionOrder instanceof SubscriptionOrder && $this->subscriptionOrder->subscription->cashOnDelivery && $this->subscriptionOrder->payOrderAfter < new DateTimeImmutable())
			) {
				$closeOrder = true;
			}
			if ($this->subscriptionOrder->sequence == 1) {
				$closeOrder = true;
			}
		}

		return $closeOrder;
	}

	protected function getterHasTrackingPackageNumber(): bool
	{
		return in_array($this->transportType, [self::TRANSPORT_DPD, self::TRANSPORT_ZASILKOVNA, self::TRANSPORT_PPL, self::TRANSPORT_PPL_PARCEL], true);
	}

	protected function getterIsSubscriptionOrder(): bool
	{
		return $this->subscriptionOrder !== null;
	}

}
