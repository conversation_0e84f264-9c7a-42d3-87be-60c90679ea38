<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;

/**
 * @property int $id {primary}
 * @property string|null $paymentId
 * @property string|null $parentId
 * @property bool $isMain {default false}
 * @property bool $isActive {default true}
 * @property string $orderNumber {default ''}
 * @property string|null $state
 * @property string|null $paymentUrl
 * @property float $amount
 * @property string|null $response
 * @property DateTimeImmutable $updated {default 'now'}
 * @property DateTimeImmutable $created {default 'now'}
 *
 * RELATIONS
 * @property Subscription $subscription {m:1 Subscription::$payments}
 * @property SubscriptionOrder $subscriptionOrder {m:1 SubscriptionOrder::$payments}
 *
 * VIRTUAL
 * @property-read Mutation $mutation {virtual}
 */
class SubscriptionPayment extends Entity
{

	use hasConfigServiceTrait;
	use hasOrmTrait;

	public const SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED';

	public const STATUS_PAID = 'PAID';

	public function getterMutation(): Mutation
	{
		return $this->subscription->mutation;
	}

	public function cancel(): void
	{
		$this->isActive = false;
		$this->state = self::SUBSCRIPTION_CANCELLED;
		$this->orm->subscriptionPayment->persistAndFlush($this);
	}

}
