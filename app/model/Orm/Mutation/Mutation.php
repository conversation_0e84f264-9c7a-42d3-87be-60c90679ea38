<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\MutationSetting\IsMutationSettingCompatible;
use App\Model\Orm\MutationSetting\MutationSetting;
use App\Model\Orm\NewsletterCampaign\NewsletterCampaign;
use App\Model\Orm\SearchLog\SearchLog;
use App\Model\Orm\SearchLogDetail\SearchLogDetail;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use RuntimeException;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasConstsTrait;
use SuperKoderi\hasMutationSettingModelTrait;
use function count;
use function filter_var;
use function implode;
use function is_array;
use function strlen;
use function trigger_error;
use const E_USER_WARNING;
use const FILTER_VALIDATE_EMAIL;

/**
 * @property int $id {primary}
 * @property string|null $location
 * @property int $public {default 1}
 * @property string|null $name
 * @property string|null $langCode
 * @property string|null $langMenu
 * @property string|null $domain
 * @property string|null $urlPrefix
 * @property string|null $region https://app.sistrix.com/en/hreflang-generator
 * @property int $rootId
 * @property int|null $hidePageId
 *
 * @property int $favouritePointsSort {default 0}
 * @property int $animalPointsSort {default 0}
 * @property int $actionPointsSort {default 0}
 * @property float|null $transportFreeFrom
 * @property string|null $countryDefault
 * @property bool $isEshop {default 0}
 * @property bool $isFilterOff {default 0}
 * @property bool $subscriptionAllowed {default 0}
 * @property array $synonyms {wrapper JsonContainer}
 * @property array $countryList {wrapper JsonContainer}
 * @property string|null $mailChimpApiKey
 * @property string|null $mailChimpListId
 * @property string|null $mailChimpStoreId
 * @property string|null $currency
 * @property string|null $currencyShow
 * @property int $roundPositions {default 2}
 * @property int $roundPositionsExtended {default null}
 * @property float $vatDefault {default 0.0}
 * @property int|null $numberPrefix prefix ciselnych rad obj, fa, dobropisy
 * @property int|null $rootLibraryId
 *
 * RELATIONS
 * @property OneHasMany|EsIndex[] $esIndexes  {1:m EsIndex::$mutation}
 * @property OneHasMany|Product[] $products  {1:m Product::$mutation}
 * @property OneHasMany|MutationSetting[] $mutationSettings  {1:m MutationSetting::$mutation}
 * @property OneHasMany|Seolink[] $seolinks  {1:m Seolink::$mutation}
 * @property OneHasMany|Flag[] $flags  {1:m Flag::$mutation}
 * @property OneHasMany|Place[] $places  {1:m Place::$mutation}
 * @property OneHasMany|Redirect[] $redirects  {1:m Redirect::$mutation}
 * @property OneHasMany|EmailTemplate[] $emailTemplates  {1:m EmailTemplate::$mutation}
 * @property OneHasMany|Message[] $messages  {1:m Message::$mutation}
 * @property OneHasMany|User[]|null $users  {1:m User::$mutation}
 * @property OneHasMany|Order[]|null $orders  {1:m Order::$mutation}
 *
 * @property OneHasMany|MutationTransports[]|null $transports {1:m MutationTransports::$mutation}
 * @property OneHasMany|MutationPayments[]|null $payments {1:m MutationPayments::$mutation}
 * @property OneHasMany|MutationBreeds[]|null $breeds {1:m MutationBreeds::$mutation}
 * @property OneHasMany|UserGroup[]|null $userGroups {1:m UserGroup::$mutation}
 * @property ManyHasMany|User[]|null $userPartners {m:m User::$partnerMutations}
 * @property OneHasMany|Voucher[] $vouchers  {1:m Voucher::$mutation}
 * @property OneHasMany|NewsletterEmail[] $newsletterEmails  {1:m NewsletterEmail::$mutation}
 * @property OneHasMany|NewsletterCampaign[] $newsletterCampaigns  {1:m NewsletterCampaign::$mutation}
 * @property OneHasMany|SearchLog[] $searchLogs {1:m SearchLog::$mutation}
 * @property OneHasMany|SearchLogDetail[] $searchLogDetails {1:m SearchLogDetail::$mutation}
 * @property OneHasMany|ProductVariantPriceLog[] $priceLogs {1:m ProductVariantPriceLog::$mutation, cascade=[persist, remove]}
 * @property OneHasMany|LotteryTicket[] $lotteryTickets {1:m LotteryTicket::$mutation}
 *
 * VIRTUAL
 * @property-read array $formatSynonyms {virtual}
 * @property-read array $transportsArray {virtual}
 * @property-read array $subscriptionTransportsArray {virtual}
 * @property-read array $paymentsArray {virtual}
 * @property-read array $subscriptionPaymentsArray {virtual}
 * @property-read array $subscriptionSubTypePaymentsArray {virtual}
 * @property-read string $emailFrom {virtual}
 * @property-read string $emailFromName {virtual}
 * @property-read array $invoiceData {virtual}
 * @property-read array $orderApiConfig {virtual}
 * @property-read bool $isSubscriptionEnabled {virtual}
 * @property-read float $subscriptionSale {virtual}
 */
class Mutation extends Entity
{
	use hasConstsTrait;
	use hasConfigServiceTrait;
	use hasMutationSettingModelTrait;

	public const CODE_CS = 'cs';
//	public const CODE_EN = 'en';
	public const CODE_SK = 'sk';
	public const CODE_PL = 'pl';

	public const SYNONYMS_DELIMITER = ',';

	public const ID_CS_DEFAULT = 1;
	public const ID_EU_INTERNATIONAL = 2;
	public const ID_SK_ESHOP = 3;
	public const ID_PL_ESHOP = 11;
	public const ID_RO_ESHOP = 17;

	public const NUMBER_PREFIX_ADD_ORDER = 10;
	public const NUMBER_PREFIX_ADD_INVOICE = 40;
	public const NUMBER_PREFIX_ADD_CREDIT_NOTE = 70;
	const DEFAULT_CODE = 'cs';
	const DEFAULT_RS_CODE = 'cs';

	public const MUTATION_VAT_CHOICE = [
		self::ID_PL_ESHOP => [
			8 => 8,
			23 => 23,
		],
		self::ID_RO_ESHOP => [
			9 => 9,
			19 => 19,
		],
	];

	protected function getterRoundPositionsExtended(int|null $roundPositionsExtended): int
	{
		if (isset($roundPositionsExtended)) {
			return $roundPositionsExtended;
		}

		return $this->roundPositions ?? 2;
	}

	public function getDomain(): string
	{
		$domainInDb = $this->domain;
		$domain = $this->configService->getParam('lang', (string) $this->langCode);

		$domain = (!isset($domainInDb) || $domainInDb == '') && isset($domain['domain'])
			? $domain['domain']
			: $domainInDb;

		return $domain;
	}

	public function getGTMCode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('lang', (string) $this->langCode);

		return $domain['gtmCode'] ?? '';
	}

	public function getGACode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('lang', (string) $this->langCode);

		return $domain['googleAnalyticsCode'] ?? '';
	}

	public function getFBCode(): string|null
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$code = $this->configService->getParam('lang', (string) $this->langCode)['fbCode'] ?? '';

		return (isset($code) && Strings::length($code) > 1) ? $code : null;
	}

	protected function getterFormatSynonyms(): array
	{
		$list = [];
		foreach ($this->synonyms ?? [] as $word => $synonyms) {
			$list[$word] = implode(self::SYNONYMS_DELIMITER, $synonyms);
		}

		return $list;
	}

	protected function getterTransportsArray(): ArrayHash
	{
		$transports = new ArrayHash();

		$actualTransports = $this->transports->toCollection()->orderBy('sort')->fetchAll();
		if ($actualTransports !== null) {
			foreach ($actualTransports as $item) {
				$transports[$item->key] = ArrayHash::from($item->toArray());
			}
		}

		return $transports;
	}

	protected function getterSubscriptionTransportsArray(): ArrayHash
	{
		$transports = new ArrayHash();

		$actualTransports = $this->transports;
		if ($actualTransports !== null) {
			foreach ($actualTransports as $item) {
				if (in_array($item->key, MutationTransports::ALLOWED_FOR_SUBSCRIPTION, true)) {
					$transports[$item->key] = ArrayHash::from($item->toArray());
				}
			}
		}

		return $transports;
	}


	protected function getterPaymentsArray(): array
	{
		$payments = [];

		$actualPayments = $this->payments;
		if ($actualPayments !== null) {
			foreach ($actualPayments as $item) {
				$payments[$item->key] = $item->toArray();
			}
		}

		return $payments;
	}

	protected function getterSubscriptionPaymentsArray(): array
	{
		$payments = [];

		$actualPayments = $this->payments;
		if ($actualPayments !== null) {
			foreach ($actualPayments as $item) {
				if (in_array($item->key, MutationPayments::ALLOWED_FOR_SUBSCRIPTION, true)) {
					$payments[$item->key] = $item->toArray();
				}
			}
		}

		return $payments;
	}

	protected function getterSubscriptionSubTypePaymentsArray(): array
	{
		$payments = [];

		$actualPayments = $this->payments;
		if ($actualPayments !== null) {
			foreach ($actualPayments as $item) {
				if (in_array($item->key, MutationPayments::ALLOWED_FOR_SUBSCRIPTION_SUBTYPE, true)) {
					$payments[$item->key] = $item->toArray();
				}
			}
		}

		return $payments;
	}

	public function getStates(): array
	{
		$states = [];

		if (count($this->countryList) > 0) {
			foreach ($this->countryList as $l) {
				$states[$l] = 'state_' . $l;
			}
		}

		return $states;
	}

	protected function getterEmailFrom(): mixed
	{
		return $this->configService->get('lang', $this->langCode, 'emailFrom');
	}

	protected function getterEmailFromName(): mixed
	{
		return $this->configService->get('lang', $this->langCode, 'emailFromName');
	}

	protected function getterInvoiceData(): mixed
	{
		return $this->configService->get('lang', $this->langCode, 'invoiceData');
	}

	protected function getterOrderApiConfig(): mixed
	{
		return $this->configService->get('lang', $this->langCode, 'orderApi');
	}

	public function getSetting(IsMutationSettingCompatible $enum): mixed
	{
		$key = $enum->key();

		$mutationFromNeon = $this->configService->getParam('lang', (string) $this->langCode);
		if (is_array($mutationFromNeon) && isset($mutationFromNeon[$key])) {
			bd('NEON setting used:');
			bd($mutationFromNeon[$key]);
			if (filter_var($mutationFromNeon[$key], FILTER_VALIDATE_EMAIL) !== false) {
				return $mutationFromNeon[$key];
			} else {
				trigger_error('Email in configuration is not valid. Check please mutation settings or hardcoded .neon config files.', E_USER_WARNING);
			}
		}

		return $this->mutationSettingModel->getValue($this, $enum);
	}

	public function setSetting(IsMutationSettingCompatible $enum, mixed $value): MutationSetting
	{
		$setting = $this->mutationSettingModel->save($this, $enum, $value);

		$setting->getRepository()->flush();

		return $setting;
	}

	public function getterIsSubscriptionEnabled(): bool
	{
		return $this->subscriptionAllowed && $this->isEshop;
	}

	/**
	 * is mutation set for production?
	 * it depends on settings "robots" => 'index, follow'
	 *
	 * @return bool
	 */
	public function isInProduction(): bool {
		$lang = $this->configService->getParam('lang', (string) $this->langCode);
		return  (!isset($lang["robots"]) || str_starts_with($lang["robots"], 'index'));
	}

	public function getterSubscriptionSale(): float
	{
		$subscriptionSale = 0.0;

		$subscriptionUserGroup = $this->userGroups->toCollection()->getBy(['type' => UserGroup::TYPE_FIRST_SUBSCRIPTION, 'mutation' => $this]);

		if ($subscriptionUserGroup instanceof UserGroup) {
			$discount = $subscriptionUserGroup->discounts->toCollection()->fetch();
			$subscriptionSale = $discount ? $discount->value : $subscriptionSale;
		}

		return $subscriptionSale;
	}

}
