<?php

namespace App\Model;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $pathString
 * @property int|null $last
 * @property int|null $level
 * @property int|null $sort
 * @property int|null $created
 * @property int|null $edited
 * @property string|null $name
 * @property string|null $nameTitle
 * @property string|null $nameAnchor
 * @property DateTimeImmutable $createdTime {default "now"}
 * @property DateTimeImmutable $editedTime {default "now"}
 * @property DateTimeImmutable|null $publicFrom
 * @property DateTimeImmutable|null $publicTo
 * @property string|null $uid
 *
 * VIRTUAL
 * @property array|null $path {virtual}
 *
 * RELATIONS
 * @property LibraryTree|null $parent {m:1 LibraryTree::$crossroad}
 * @property LibraryTree[]|OneHasMany $crossroad {1:m LibraryTree::$parent, orderBy=[sort=ASC]}
 * @property OneHasMany|LibraryImage[] $images {1:m LibraryImage::$library, orderBy=[sort=ASC]}
 *
 */
class LibraryTree extends \Nextras\Orm\Entity\Entity
{

	const ROOT_ID = 1;
	const DEFAULT_ID = 2; // výchozí složka
	const NOVIKO_IMPORT_ID = 3;
	const USER_ANIMAL_ID = 6;


	protected function getterPath()
	{
		if (!isset($this->id)) {
			return NULL;
		}

		$path = preg_split('~\|~', $this->pathString ?? '', -1, PREG_SPLIT_NO_EMPTY);
		$path = array_map('intval', $path);
		return $path;
	}


	protected function setterPath($path = [])
	{
		if ($path) {
			$this->pathString = implode('|', $path) . '|';
		} else {
			$this->pathString = null;
		}
	}
}
