<?php

namespace App\Model;

use Nextras\Orm;

/**
 * @property int $id {primary}
 * @property EmailTemplate|null $emailTemplate {m:1 EmailTemplate::$files}
 * @property string $name
 * @property string $url
 * @property string $size
 * @property int $file
 * @property int $sort
 *
 * VIRTUALS
 * @property string $ext {virtual}
 * @property string $filename {virtual}
 */
class EmailTemplateFile extends Orm\Entity\Entity
{
	public function getterFilename()
	{
		return substr($this->url, strrpos($this->url, '/') + 1);
	}

	protected function getterExt()
	{
		return substr($this->url, strrpos($this->url, '.') + 1);
	}
}
