<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Entity\Entity;
use function explode;
use function strrpos;
use function substr;

/**
 * @property int $id {primary}
 * @property string $name
 * @property string $url
 * @property int|null $sort
 * @property int $image
 * @property string|null $variants
 * @property Product[] $product {m:1 Product::$images}
 * @property string $filename {virtual}
 */
class ProductImage extends Entity implements IEntityImage
{

	public function getterFilename(): string
	{
		return substr($this->url, (int) strrpos($this->url, '/') + 1);
	}

	public function getFilename(): string
	{
		return $this->filename;
	}

	public function getVariantIds(): array
	{
		return explode('|', $this->variants ?? '');
	}

}
