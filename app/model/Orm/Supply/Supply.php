<?php

namespace App\Model;

use <PERSON>bi\DateTime;

/**
 * @property int $id {primary}
 * @property int $amount {default 0}
 *
 *
 * RELATIONS
 * @property Stock $stock {m:1 Stock::$supplies}
 * @property ProductVariant $variant {m:1 ProductVariant::$supplies}
 *
 *
 * VIRTUALS
 */
class Supply extends \Nextras\Orm\Entity\Entity
{


	/**
	 * vraci retezec typu:
	 *
	 * zítra 20. 10. u vás
	 * v pondělí 24. 10. u vásh
	 *
	 * pr:
	 * pondeli, 11h, -> utery
	 * pondeli, 13h, -> streda
	 * patek, 11h -> pondeli
	 * patek, 13h -> utery
	 * sobota/nedele, 11/13h -> utery
	 *
	 * @return string
	 */
	public function getDeliveryTime($wantToBuy = NULL, $transportType = NULL)
	{
		$amount = $this->amount;

		if ($transportType == 'personalSpecial') {
			$amount = 10000000;
		}

		if ($this->variant->product->voucher && $this->variant->product->voucher->public) {
			// je to poukaz
			$amount = 10000000;
		}
		$now = new \DateTime();
		$timeHandler = new \SuperKoderi\TimeHandler();

		if ($wantToBuy && ($amount - $wantToBuy) < 0) {
			// mam vlozeno v kosiku vic kusu nez je skladem
			$amount = 0;
		}

		// posunuto doruceni pro konkretni dopravy
		if ($transportType == NULL || $transportType == 'personal') {
			$add = 0;
		} elseif ($transportType == Order::TRANSPORT_DPD) {
			$add = 0;
		} elseif ($transportType == Order::TRANSPORT_DPDSK) {
			$add = 1;  //+ 1 den
		} elseif ($transportType == Order::TRANSPORT_DPDEUROPE) {
			$add = 2; // 2-7 dní
		} else {
			$add = 0;
		}

		// pravidla pro kazdý typ skladu
		if ($this->stock->alias == Stock::ALIAS_SHOP) {

			if ($transportType == NULL || $transportType == 'personal' || $transportType == 'personalSpecial') {

				if ($amount > 0) {
					//TODO hlidat otviraci dobu?
//				return array("text" => "ihned k odběru");
					return array(
						"dateTime" => new DateTime(),
						"text" => "ihned k odběru",
						"isTomorrow" => FALSE,
						"isToday" => TRUE,
						"stockId" => 1,
					);

				} else {
					return NULL;

					if ($now->format("H") < 12) {
						// pred 12h
						$timeInfo = $timeHandler->getNthWorkDay($now, 1, 1); // první prac. den
						return $timeInfo;

					} else {
						$timeInfo = $timeHandler->getNthWorkDay($now, 2, 1); // druhý prac. den
						return $timeInfo;
					}
				}
			} else {
				if ($amount > 0) {
					$days = 1;
				} else {
					$days = 2;
					return NULL;
				}
				$days += $add;

				if ($now->format("H") < 12) {
					// pred 12h
					$timeInfo = $timeHandler->getNthWorkDay($now, $days + 0, 1); // první prac. den
					return $timeInfo;

				} else {
					$timeInfo = $timeHandler->getNthWorkDay($now, $days + 1, 1); // druhý prac. den
					return $timeInfo;
				}
			}
		}

		if ($this->stock->alias == Stock::ALIAS_SUPPLIER_STORE) {
			if ($amount > 0) {
				$days = 1;
			} else {
				$days = 2;
				return NULL;

			}
			$days += $add;

			if ($now->format("H") < 12) {
				// pred 12h
				$timeInfo = $timeHandler->getNthWorkDay($now, $days + 1, 2); // první prac. den
				return $timeInfo;

			} else {
				$timeInfo = $timeHandler->getNthWorkDay($now, $days + 1, 2); // druhý prac. den
				return $timeInfo;
			}
		}
	}
}
