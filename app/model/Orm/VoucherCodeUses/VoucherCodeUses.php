<?php

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $usedBy {default 0}
 * @property DateTimeImmutable $created {default 'now'}
 *
 *
 * RELATIONS
 * @property VoucherCode $voucherCode {m:1 VoucherCode::$voucherCodeUses}
 * @property Order|null $order {m:1 Order::$voucherCodeUses}
 *
 *
 * VIRTUAL
 *
 */
class VoucherCodeUses extends Entity
{

}
