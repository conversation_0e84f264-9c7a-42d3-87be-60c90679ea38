<?php declare(strict_types = 1);

namespace SuperKoderi;

use App\Model\Parameter;
use App\Model\ParameterValue;
use App\Model\Product;
use App\Model\ProductVariant;
use ArrayIterator;

trait HasProductVariant
{

	protected static $PARAMS = [
		Parameter::UID_PRODUCT_LINE => 'produktova_rada',
		Parameter::UID_DOG_SIZE => 'plemeno',
		Parameter::UID_PROTEIN => 'pref_protein',
		Parameter::UID_ANIMAL_TYPE => 'typ_krmiva',
		Parameter::UID_PRODUCT_TYPE => 'typ_produktu',
		Parameter::UID_AGE_CAT => 'vek', //only one age should be set
		Parameter::UID_AGE_DOG => 'vek',
	];

	public function getProductVariant(ProductVariant $productVariant, ?float $price = null, ?int $amount = null, ?string $list = null, ?int $position = null): array
	{
		$name = $productVariant->nameDefault;
		$variantName = $productVariant->nameWeight;
		$price = $price ?? ($this->gtm->getMutation()->isEshop ? $productVariant->priceFinalDPH : null);
		$price = isset($price) ? ['price' => $price] : [];
		$quantity = isset($amount) ? ['quantity' => $amount] : [];
		$list = isset($list) ? ['list' => $list] : [];
		$position = isset($position) ? ['position' => $position] : [];

		$params = [];
		foreach (self::$PARAMS as $uid => $key) {
			$pValue = $productVariant->getParameterValueByUid($uid);
			if ($pValue instanceof ArrayIterator) {
				foreach ($pValue as $value) {
					$string = $value->getTranslation($this->gtm->getMutation(), ParameterValue::TRANS_TYPE_VALUE);
					$params[$key][] = isset($string) ? $string->value : null;
				}

				if (count($params[$key]) === 1) {
					$params[$key] = $params[$key][0];
				}
			} elseif ($pValue instanceof ParameterValue) {
				$string = $pValue->getTranslation($this->gtm->getMutation(), ParameterValue::TRANS_TYPE_VALUE);
				$params[$key] = isset($string) ? $string->value : null;
			}
		}

		return [
			'name' => $name,
			'id' => $productVariant->product->id,
			'brand' => $this->getBrand($productVariant->product),
			'variant' => $variantName,
		] + $price + $quantity + $params + $list + $position;
	}

	protected function getBrand(Product $product): string
	{
		return 'Calibra'; //so far...
	}

}
