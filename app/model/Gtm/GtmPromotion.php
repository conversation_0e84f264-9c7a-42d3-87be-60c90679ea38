<?php declare(strict_types = 1);

namespace SuperKoderi;

use stdClass;

class GtmPromotion extends stdClass
{

	private string $id;

	private string $name;

	private ?string $creative;

	private int $position;

	public function __construct(string $id, string $name, ?string $creative, int $position)
	{
		$this->id = $id;
		$this->name = $name;
		$this->creative = $creative;
		$this->position = $position;
	}

	public function asArray(): array
	{
		return [
			'id' => $this->id,
			'name' => $this->name,
			'creative' => $this->creative,
			'position' => $this->position,
		];
	}

}
