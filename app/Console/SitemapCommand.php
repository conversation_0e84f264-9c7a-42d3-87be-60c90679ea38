<?php declare(strict_types = 1);

namespace App\Console;

use RuntimeException;
use SuperKoderi\Console\Model\Sitemap;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class SitemapCommand extends Command
{

	use LockableTrait;

	/** @var string|null */
	protected static $defaultName = 'sitemap'; //phpcs:ignore

	public function __construct(private readonly Sitemap $sitemap)
	{
		parent::__construct(null);
	}

	protected function configure(): void
	{
		$this->setDescription('generate sitemap for page');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		if (!$this->lock()) {
			throw new RuntimeException('The command is already running in another process.');
		}

		$this->sitemap->create();

		$output->writeln('DONE');

		return self::SUCCESS;
	}

}
