<?php declare(strict_types = 1);

/** @noinspection PhpUnused */
/** @noinspection PhpUndefinedMethodInspection */
/** @noinspection PhpUndefinedClassInspection */

namespace App\Console\Eshop;

use App\Model\Order;
use App\Model\OrderItem;
use App\Model\Orm;
use Nette\Utils\DateTime;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class BestSellerCommand extends Command
{

	/** @var string|null */
	protected static $defaultName = 'eshop:product:soldcount'; //phpcs:ignore

	public function __construct(private readonly Orm $orm)
	{
		parent::__construct(null);
	}

	protected function configure(): void
	{
		$this->setDescription('Recalculate soldCount for bestseller');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
			$output->writeln('START');
			$from = DateTime::from('now')->modify('- 1 year')->format('Y-m-d');

			$orderItems = $this->orm->orderItem->findBy([
				'order->mutation->isEshop' => 1,
				'order->status' => Order::STATUS_DONE,
				'type' => OrderItem::TYPE_PRODUCT,
				'order->created>=' => $from,
			]);

			$amountList = [];
		foreach ($orderItems as $orderItem) {
			if (isset($amountList[$orderItem->productId])) {
				$amountList[$orderItem->productId]++;
			} else {
				$amountList[$orderItem->productId] = 1;
			}
		}

			$products = $this->orm->product->findBy([
				'mutation->isEshop' => 1,
			]);

		foreach ($products as $product) {
			$product->soldCount = $amountList[$product->id] ?? 0;

			$this->orm->persist($product);
		}

			$this->orm->flush();

			$output->writeln('DONE');

			return 0; // zero return code means everything is ok
	}

}
