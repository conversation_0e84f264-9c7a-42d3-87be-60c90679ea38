<?php declare(strict_types = 1);

namespace App\Console\Import;

use App\Model\Orm;
use App\Model\TreeModel;
use <PERSON><PERSON>\Connection;
use RuntimeException;
use SuperKoderi\Fnc;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use function count;

class TreeCloneCommand extends Command
{

	/** @var string|null */
	protected static $defaultName = 'import:tree:clone'; //phpcs:ignore

	public function __construct(private readonly Orm $orm, private readonly TreeModel $treeModel, private readonly Connection $db)
	{
		parent::__construct(null);
	}

	protected function configure(): void
	{
		$this->setDescription('Clone product to mutations');
		$this->addOption('idFrom', 'f', InputOption::VALUE_REQUIRED, 'Source tree root ID');
		$this->addOption('idTo', 't', InputOption::VALUE_REQUIRED, 'Destination tree root ID');
		$this->addOption('mutationFrom', 'm', InputOption::VALUE_REQUIRED, 'Source mutation code');
		$this->addOption('mutationTo', 'd', InputOption::VALUE_REQUIRED, 'Destination mutation code');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		new Fnc($this->db);

		$sourceId = $input->getOption('idFrom');
		$destinationId = $input->getOption('idTo');
		$sourceMutation = $input->getOption('mutationFrom');
		$destMutation = $input->getOption('mutationTo');

		$output->writeln('Cloning tree from ' . $sourceMutation . ' to ' . $destMutation);

		$sourceTree = $this->orm->tree->getByIdChecked($sourceId);
		if (count($sourceTree->childsId) === 0) {
			throw new RuntimeException('Source tree has no children.');
		}

		$destTree = $this->orm->tree->getByIdChecked($destinationId);
		if (count($destTree->childsId) > 0) {
			throw new RuntimeException('Destination tree has children, it must be empty.');
		}

		$this->orm->mutation->getByCodeChecked($sourceMutation);
		$this->orm->mutation->getByCodeChecked($destMutation);

		$this->treeModel->onDuplicate[] = static function (int $sourceId, int $destId) use ($output): void {
			$output->writeln($sourceId . ' => ' . $destId);
		};

		$this->treeModel->handleDuplicate($sourceId, $destinationId, null, $sourceMutation, $destMutation, true, true);

		$output->writeln('DONE');

		return self::SUCCESS;
	}

}
