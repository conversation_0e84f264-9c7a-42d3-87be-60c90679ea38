<?php declare(strict_types = 1);

namespace SuperKoderi\Console\Model;

use App\Model\Orm;
use App\Model\Product;
use App\Model\Seolink;
use App\Model\Tree;
use DateTime;
use Dibi\Connection;
use Exception;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\ConfigService;
use SuperKoderi\Fnc;
use SuperKoderi\SkLinkGenerator;
use function dirname;
use function file_exists;
use function file_put_contents;
use function is_string;
use function mkdir;
use function sprintf;
use function strlen;
use const FILE_APPEND;

class Sitemap
{

	private string $wwwDir;

	private DateTime $now;

	/** @var array<mixed> */
	private array $langsSetup;

	public function __construct(
		private readonly Connection $db,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly SkLinkGenerator $skLinkGenerator,
	)
	{
		$this->wwwDir = $configService->getParam('WWW_DIR');
		$this->langsSetup = $configService->getParam('lang');
		$this->now = new DateTime();
	}

	public function create(): void
	{
		new Fnc($this->db);
		$this->generateRootFile();
	}

	public function generateRootFile(): void
	{
		$file = $this->wwwDir . '/sitemap.xml';

		$this->replaceFileContent($file, $this->getXMLStrings('beginIndex'));

		foreach ($this->langsSetup as $langKey => $langSetup) {

			if (isset($langSetup['sitemap']) && $langSetup['sitemap'] == true) {
				// ok
			} else {
				continue;
			}

			$langUrl = (is_string($langSetup['domain'] ?? null) && strlen($langSetup['domain']) > 0)
				? $langSetup['domain']
				: $this->configService->getParam('domainUrl');

			if (is_string($langSetup['urlPrefix'] ?? null) && strlen($langSetup['urlPrefix']) > 0) {
				$langUrl .= $langSetup['urlPrefix'] . '/';
			}

			$langFile = $this->wwwDir . '/sitemaps/sitemap-' . $langKey . '.xml';

			$this->appentToFile($file, sprintf(
				$this->getXMLStrings('rowSitemap'),
				$langUrl . '/sitemaps/sitemap-' . $langKey . '.xml',
				$this->now->format('Y-m-d'),
			));

			$this->generateLangFile($langKey, $langFile, $langUrl);
		}

		$this->appentToFile($file, $this->getXMLStrings('endIndex'));
	}

	private function generateLangFile(string $lang, string $file, string $langUrl): void
	{
		$mutation = $this->orm->mutation->getByChecked(['langCode' => $lang]);
		$this->orm->setMutation($mutation);

		if (!file_exists(dirname($file))) {
			mkdir(dirname($file), 0777, true);
		}

		// tree
		if (isset($this->langsSetup[$lang]['treeId'])) {
			$rootPage = $this->orm->tree->getById($this->langsSetup[$lang]['treeId']);

			if (isset($rootPage)) {
				$this->replaceFileContent($file, $this->getXMLStrings('begin'));
				$this->writeObject($rootPage, $file, $lang);
			}
		}

		// produkty
		$cond = [
			'mutation' => $this->langsSetup[$lang]['mutationId'],
			'isSale' => 1,
			'isGift' => 0,
		];
		foreach ($this->orm->product->findBy($cond) as $product) {
			$this->writeObject($product, $file, $lang);
		}

		// seo filtry
		$cond = [
			'mutation' => $this->langsSetup[$lang]['mutationId'],
			'isActive' => 1,
			'isDefault' => 0,
			'noIndex' => 0,
		];
		foreach ($this->orm->seolink->findBy($cond) as $seolink) {
			$this->writeObject($seolink, $file, $lang);
		}

		$this->appentToFile($file, $this->getXMLStrings('end'));
	}

	private function writeObject(IEntity $obj, string $file, string $lang, array $params = [], int $priority = 1): void
	{
		if ($obj instanceof Tree) {
			if (!(bool) $obj->hideInSitemap) {
				$this->appentToFile($file, sprintf(
					$this->getXMLStrings('row'),
					$this->skLinkGenerator->linkFromAlias($obj->alias, $lang, $params),
					$this->now->format('Y-m-d'),
					$priority,
				));
			}

			if (isset($obj->crossroad) && $obj->crossroad->count() > 0) {
				foreach ($obj->crossroad as $item) {
					$this->writeObject($item, $file, $lang, $params);
				}
			}
		} elseif ($obj instanceof Product) {
			$productVariant = $obj->firstActiveVariant;
			if (isset($productVariant)) {
				$this->appentToFile($file, sprintf(
					$this->getXMLStrings('row'),
					$this->skLinkGenerator->linkFromAlias($obj->alias, $lang, $params),
					$this->now->format('Y-m-d'),
					$priority,
				));
			}
		} elseif ($obj instanceof Seolink) {
			$this->appentToFile($file, sprintf(
				$this->getXMLStrings('row'),
				$this->skLinkGenerator->linkFromAlias($obj->url, $lang, $params),
				$this->now->format('Y-m-d'),
				$priority,
			));
		}
	}

	private function appentToFile(string $file, string $content): void
	{
		$this->contentToFile($file, $content, true);
	}

	private function replaceFileContent(string $file, string $content): void
	{
		$this->contentToFile($file, $content);
	}

	private function contentToFile(string $file, string $content, bool $append = false): void
	{
		$res = $append ? file_put_contents($file, $content, FILE_APPEND) : file_put_contents($file, $content);

		if ($res === false) {
			throw new Exception('Cant write sitemap file - ' . $file);
		}
	}

	private function getXMLStrings(string $type): string
	{
		switch ($type) {
			case 'begin':
				$s = '<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">';

				break;
			case 'end':
				$s = '
</urlset>';

				break;
			case 'beginIndex':
				$s = '<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">';

				break;
			case 'endIndex':
				$s = '
</sitemapindex>';

				break;
			case 'row':
				$s = '
	<url>
		<loc>%s</loc>
		<lastmod>%s</lastmod>
		<changefreq>daily</changefreq>
		<priority>%s</priority>
	</url>';

				break;
			case 'rowSitemap':
				$s = '
	<sitemap>
		<loc>%s</loc>
		<lastmod>%s</lastmod>
	</sitemap>';

				break;
			default:
				throw new Exception('Unknown sitemap XML string type - ' . $type);
		}

		return $s;
	}

}
