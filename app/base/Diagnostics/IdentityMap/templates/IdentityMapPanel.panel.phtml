<?php

namespace Nette\Bridges\DITracy;

use <PERSON><PERSON>,
	<PERSON>\Dumper;

?>
<style class="tracy-debug">#tracy-debug .nette-IdentityMapPanel .tracy-inner{width:300px}#tracy-debug .nette-IdentityMapPanel table{width:100%;white-space:nowrap}#tracy-debug .nette-IdentityMapPanel .created{font-weight:bold}#tracy-debug .nette-IdentityMapPanel .yes{color:green;font-weight:bold}</style>

<div class="nette-IdentityMapPanel">
<h1>Identity Map</h1>

<div class="tracy-inner">
	<table>
		<thead>
		<tr>
			<th><PERSON><PERSON><PERSON><PERSON></th>
			<th>Použití</th>
		</tr>
		</thead>
		<tbody>

			<?php if (isset($im->stats()['usage'])) { ?>

			<?php foreach ($im->stats()['usage'] as $key => $i): ?>
				<tr>
					<td><?php echo $key; ?></td>
					<td><?php echo $i; ?></td>
				<tr>
			<?php endforeach ?>

			<?php } ?>

				<!--			<td>-->
<!--				--><?php //foreach ($im->stats()['keys'] as $key => $i): ?>
<!---->
<!--					--><?php //echo "<strong>".$key."</strong><br>";?>
<!--					--><?php //foreach ($i as $i2):
//						echo $i2."<br>";
//					 endforeach ?>
<!---->
<!--				--><?php //endforeach ?>
<!--			</td>-->
<!--		</tr>-->
		</tbody>
	</table>

</div>
</div>
