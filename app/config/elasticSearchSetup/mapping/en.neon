common:
	properties:
		name:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		annotation:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'

		content:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"

product:
	properties:
		nameSort:
			type: "keyword"
		name:
			type: "text"
			fielddata: true
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'

		content:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'

		annotation:
			type: "text"
			fields:
				dictionary:
					type: text
					analyzer: 'dictionary'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"
