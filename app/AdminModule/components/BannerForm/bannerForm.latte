{form form}

	<div class="grid-row">
		<div class="grid-2-5">
			{snippet tree}
				{if isset($tree)}
					<div class="menu-tree-cats" data-click="{plink default}" data-create="{plink create}" data-move="{plink move}">
						<ul>
							{include RS_TEMPLATE_DIR . '/Banner/part/menu.latte', 'menu' => $tree, 'active' => $selectedCategories}
						</ul>
					</div>
				{/if}
			{/snippet}

		</div>
		<div class="grid-3-5">
			<div class="grid-row">
				<p class="grid-1-3">
					{label name /}
					<span class="inp-fix">
						{input name class => 'inp-text' }
					</span>
				</p>
				<p class="grid-1-3">
					{label position /}
					<span class="inp-fix">
						{input position class => 'inp-text'}
					</span>
				</p>
			</div>
			<div class="grid-row">
				<p class="grid-1-3">
					{label link /}
					<span class="inp-fix">
						{input link class => 'inp-text'}
					</span>
				</p>

				<p class="grid-1-3">
					<span class="inp-item inp-center">
						{input newWindow: value=>1}
						{label newWindow: /}
					</span>
				</p>
			</div>

			<div class="grid-row">
				<p class="grid-1-3">
					<label n:name="publicFrom">Platnost od</label><br>
					<span class="inp-fix inp-icon-after">
						<span class="icon icon-calendar"></span>
						<input n:name="publicFrom" class="inp-text w-full inp-datetime-entry-hour">
					</span>
				</p>

				<p class="grid-1-3">
					<label n:name="publicTo">Platnost do</label><br>
					<span class="inp-fix inp-icon-after">
						<span class="icon icon-calendar"></span>
						<input n:name="publicTo" class="inp-text w-full inp-datetime-entry-hour">
					</span>
				</p>


			</div>

			<div class="grid-row">
				<p class="grid-1-3">
					<span class="inp-item inp-center">
						{input public: value=>1}
						{label public: /}
					</span>
				</p>
				<p class="grid-1-3">
					<span class="inp-item inp-center">
						{input enabledInChilds: value=>1}
						{label enabledInChilds: /}
					</span>
				</p>
			</div>

			<div id="tab-images" class="tab-fragment">
				<p class="btns-attached">
					<a class="btn btn-icon-before thickbox" data-skbox-title="{_library}" data-library="attached-images" href="/superadmin/library/?id=1">
						<span>
							<span class="icon icon-attachment"></span>
							Připojit obrázky
						</span>
					</a>
				</p>
				<div class="crossroad-images crossroad-grid">
					<ul class="sortable reset ui-sortable" id="attached-images" data-place="images">
						{if isset($banner) && $banner->images}
							{foreach $banner->images as $image}

								<li data-id="{$image->id}">
									<div class="inner">
										<div class="thumb js-handle">
											{php  $img = $imageResizer->getImg($image->filename, 's')}
											<span class="img {if isset($uploadify)}opacity-0{/if}"><img src="{$img->src}" alt="{$image->name}" /></span>
											<span class="name">{$image->name}</span>
											<a n:href="delete-image! id => $image->id" class="icon icon-close remove btn-delete"></a>
										</div>
										<div class="detail">
											<div class="detail-holder">
												<div class="grid-row">
													<p class="grid-1">
														<label for="inp-title2">{_image_name}</label><br />
														<span class="inp-fix">
															<input type="text" class="inp-text w-full" name="imageNameUpdate[{$image->id}]" id="inp-title{$image->id}" value="{$image->name}" />
														</span>
													</p>
													<p>
														{foreach $config['imageSizes'] as $k=>$i}
															{$k}: {$baseUrl}/data/images-{$k}/{$image->filename}<br />
														{/foreach}
													</p>
												</div>
											</div>
										</div>
										<input type="hidden" name="imageSortUpdate[{$image->id}]" value="{$image->sort}" class="inp-sort" />
									</div>
								</li>

							{/foreach}
						{/if}
					</ul>
				</div>
			</div>

			{*include RS_TEMPLATE_DIR . '/Page/part/customFields.latte', object=>$banner*}
			<p>Warning: CF are not connected to this form!</p>
			<br>

			<div class="grid-row">
				<p class="grid-1-5">
					<button class="btn btn-green btn-icon-before">
						<span>
							<span class="icon icon-checkmark"></span>
							{_save_button}
						</span>
					</button>
				</p>

				{if $banner && $banner->isPersisted()}
					<p class="grid-1-5">
						<a class="btn btn-red btn-icon-before btn-delete" n:href="delete">
							<span>
								<span class="icon icon-close"></span>
								{_delete_button}
							</span>
						</a>
					</p>
				{/if}
			</div>
		</div>
	</div>
{/form}
