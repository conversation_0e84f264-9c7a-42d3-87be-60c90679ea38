<?php

namespace SuperKoderi\Components;

use App\Model\Orm;
use Nette\Application\UI;
use SuperKoderi\hasTranslatorTrait;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\Translator;

class Register extends UI\Control
{
	private $urlParams;

	private $filterFactory;

	public $orm;

	private $templateName = 'register';

	public $translator;

	private $repository;
	/**
	 * @var \IVisualPaginatorFactory
	 */
	private $visualPaginatorFactory;


	public function __construct($repository, $urlParams, IFilterFactory $filterFactory, Translator $translator, Orm $orm, \IVisualPaginatorFactory $visualPaginatorFactory)
	{
		$this->repository = $repository;
		$this->orm = $orm;
		$this->urlParams = $urlParams;

		$this->translator = $translator;

		$this->filterFactory = $filterFactory;
		switch (get_class($repository)) {
			case 'App\Model\ApplicationRepository':
				$this->templateName = 'applicationRegister';
				break;
		}
		$this->visualPaginatorFactory = $visualPaginatorFactory;
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
	}

	public function render()
	{
		$visualPaginator = $this['vp'];
		$paginator = $visualPaginator->getPaginator();
		$paginator->itemsPerPage = 20;


		/*
		if (isset($this->repository->getEntityMetadata()->properties['hide'])) {
			$this->where = array_merge($this->where, ['hide' => 0]);
		}

		$items = $this->repository->findBy($this->where);
		*/

		$items = $this['filter']->getItems()->orderBy('id', ICollection::DESC);

		$paginator->itemCount = $items->countStored();

		$this->template->setTranslator($this->translator);
		$this->template->items = $items->limitBy($paginator->itemsPerPage, $paginator->offset);;
		$this->template->vp = $paginator;

		$this->template->render(__DIR__ . '/' . $this->templateName . '.latte');
	}

	protected function createComponentFilter()
	{
		return $this->filterFactory->create($this->repository, $this->urlParams, $this->templateName . '-filter');
	}

}


interface IRegisterFactory
{
	/**
	 * @return \SuperKoderi\Components\Register
	 */
	function create($repository, $urlParams);
}