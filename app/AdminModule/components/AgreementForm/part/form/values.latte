{if !isset($k)}
	{var $k='XXX'}
{else}
	{var $optionId=$k}
{/if}
{if !isset($sort)} {var $sort='XXX'} {/if}

<li xmlns="http://www.w3.org/1999/html">
	<div class="inner">
		<span class="drag-area js-handle"></span>
		<div class="grid-row">
			<div class="grid-2-3">
				<span class="inp-fix">
					<textarea rows="10" class="inp-text w-full wysiwyg" name="text[{$k}]"
							  id="inp-text{$k}">{if isset($data)}{$data->text}{/if}</textarea>

				</span>
			</div>
			<div class="grid-1-5">
				<p>
					<label for="inp-type{$k}">{_agreement_type}</label>
					<span class="inp-fix inp-fix-select">
						<select class="inp-text w-full" name="type[{$k}]" id="inp-type{$k}">
							{foreach $types as $type}
								<option value="{$type}" {if isset($data) && $data->type==$type} selected="selected"{/if}>
									{$type}
								</option>
							{/foreach}
						</select>
					</span>
				</p>
				<p>
					<br>
					<span class="inp-item">
						<input type="checkbox" class="inp-checkbox w-full" name="isPublic[{$k}]" id="inp-isPublic{$k}"
						   value="1" {if isset($data) && $data->isPublic == 1}checked="checked"{/if} />
						<label for="inp-isPublic{$k}">{_public}</label>
					</span>
				</p>
				<p>
					<br>
					<span class="inp-item">
						<input type="checkbox" class="inp-checkbox w-full" name="isRequired[{$k}]" id="inp-isRequired{$k}"
						   value="1" {if isset($data) && $data->isRequired == 1}checked="checked"{/if} />
						<label for="inp-isRequired{$k}">{_agreement_required}</label>
					</span>
				</p>
			</div>
		</div>
		<input type="hidden" name="valueSort[{$k}]" value="{$sort}" class="inp-sort"/>

		{if isset($optionId)}
			<input type="hidden" name="itemIds[]" value="{$optionId}"/>
		{/if}
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>
