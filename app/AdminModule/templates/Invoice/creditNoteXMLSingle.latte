{varType App\Model\Order $order}
{varType App\Model\CreditNote $creditNote}
{var $roundPositionsExtended = $order->mutation->roundPositionsExtended}
<doklad>
	<kniha>FV</kniha>
	<rada>{if $order->mutation->langCode == 'pl'}17{else}15{/if}</rada>
	<typ_dokladu>40</typ_dokladu>
	<k_fakture_vs>{$order->invoiceNumber}</k_fakture_vs>
	<var_symbol>{$creditNote->number}</var_symbol>
	<ext_cislo>{$creditNote->number}</ext_cislo>
	<objednavka>{$order->number}</objednavka>
	<dat_vyst>{$creditNote->created|date:'%d.%m.%Y'}</dat_vyst>
	<dat_zd_pln>{$creditNote->created|date:'%d.%m.%Y'}</dat_zd_pln>
	<dat_spl>{$creditNote->dueDate|date:'%d.%m.%Y'}</dat_spl>
	<dat_kurz>{$order->invoiceDate|date:'%d.%m.%Y'}</dat_kurz>
	<kurz>1</kurz>
	<mnozstvi>1</mnozstvi>
	<id_meny>{$order->mutation->currency ?? ""}</id_meny>
	<forma_uhrady>{if $order->cancelReason == App\Model\Order::CANCEL_REASON_RETURNED_PACKAGE}DO{else}PP{/if}</forma_uhrady>
	<id_partnera></id_partnera>
	<nazev>{$order->firstname.' '.$order->lastname}</nazev>
	<ulice>{$order->street}</ulice>
	<psc>{$order->zip}</psc>
	<obec>{$order->city}</obec>
	<stat>{$order->mutation->langMenu ?? ""}</stat>
	<email>{$order->email}</email>
	<telefon>{$order->phone}</telefon>
	<ucet>311/100</ucet>
	<stredisko>19</stredisko>
	<typ_zakazky>VET</typ_zakazky>
	<zakazka></zakazka>
	<cinnost>CAL</cinnost>
	<dealer></dealer>
	<text></text>
	<polozky>
{*		GROUPING ITEMS BY CLIENT ASSIGNMENT	*}
		{var $prices = [
		'totalPrice' => 0,
		'totalPriceDPH' => 0,
		'totalDPH' => 0,
		]}
		{var $creditNoteItems = [
		'transportAndPayment' => $prices,
		]}

		{foreach $creditNote->items as $item}
			{varType App\Model\CreditNoteItem $item}
			{if $item->isTypeTransport || $item->isTypePayment}
				{if !isset($creditNoteItems['transportAndPayment']['vat'])}
					{var $creditNoteItems['transportAndPayment']['vat'] = $item->vat}
				{/if}
				{var $creditNoteItems['transportAndPayment']['totalPrice'] = $creditNoteItems['transportAndPayment']['totalPrice'] + $item->totalPrice}
				{var $creditNoteItems['transportAndPayment']['totalPriceDPH'] = $creditNoteItems['transportAndPayment']['totalPriceDPH'] + $item->totalPriceDPH}
				{var $creditNoteItems['transportAndPayment']['totalDPH'] = $creditNoteItems['transportAndPayment']['totalDPH'] + $item->totalDPH}
			{else}
				{if !isset($creditNoteItems[(string)$item->vat])}
					{var $creditNoteItems[(string)$item->vat] = $prices}
				{/if}
				{var $creditNoteItems[(string)$item->vat]['totalPrice'] = $creditNoteItems[(string)$item->vat]['totalPrice'] + $item->totalPrice}
				{var $creditNoteItems[(string)$item->vat]['totalPriceDPH'] = $creditNoteItems[(string)$item->vat]['totalPriceDPH'] + $item->totalPriceDPH}
				{var $creditNoteItems[(string)$item->vat]['totalDPH'] = $creditNoteItems[(string)$item->vat]['totalDPH'] + $item->totalDPH}
			{/if}
		{/foreach}

		{var $ok = ksort($creditNoteItems, SORT_STRING)}

		{var $rowCounter = 1}
		{foreach $creditNoteItems as $itemGroupKey => $itemGroup}
			{continueIf $itemGroup['totalPriceDPH'] == 0}

			<polozka>
				<nomenklatura></nomenklatura>
				<nazev>Řádek {$rowCounter}</nazev>
				<sazba_dph>{if $itemGroupKey == 'transportAndPayment'}{sprintf('%02d', (int) $itemGroup['vat'])}{else}{sprintf('%02d', (int) $itemGroupKey)}{/if}</sazba_dph>
				<kod_dph>PTN{if $itemGroupKey == 'transportAndPayment'}{sprintf('%02d',(int) $itemGroup['vat'])}{else}{sprintf('%02d', (int) $itemGroupKey)}{/if}{if $order->mutation->langCode == 'pl'}PL{/if}</kod_dph>
				<mnozstvi_vyd>1</mnozstvi_vyd>
				<cena_cenik>{(round($itemGroup['totalPrice'], $roundPositionsExtended))}</cena_cenik>
				<cena_celkem>{(round($itemGroup['totalPrice'], $roundPositionsExtended))}</cena_celkem>
				<castka_dph>{round($itemGroup['totalDPH'], $roundPositionsExtended)}</castka_dph>
				<strana>1</strana>
				<ucet>{if $itemGroupKey == 'transportAndPayment'}602/125{else}604/125{/if}</ucet>
				<stredisko>1112</stredisko>
				<typ_zakazky>VET</typ_zakazky>
				<zakazka>{if $itemGroupKey == 'transportAndPayment'}15{elseif ((int)$itemGroupKey) == 15}12{elseif ((int)$itemGroupKey) == 21}13{/if}</zakazka>
				<cinnost>CAL</cinnost>
				<dealer></dealer>
				<text></text>
			</polozka>
			{var $rowCounter = $rowCounter + 1}
		{/foreach}
	</polozky>
</doklad>


