{var $props = [
	img: $props['img'] ?? '',
	name: $props['name'] ?? false,
	inps: $props['inps'] ?? [],
	btns: $props['btns'] ?? [],
	data: $props['data'] ?? [],
	isSelected: $props['isSelected'] ?? false,
	dragdrop: $props['dragdrop'] ?? false,
	progress: $props['progress'] ?? false,
]}

<div n:class="b-imgs__item, block-loader, grid__cell, size--3-12, $props['isSelected'] ? 'b-imgs__item--selected'"
	{foreach $props['data'] as $key=>$value}
		data-{$key}="{$value|noescape}"
	{/foreach}
>
	<div class="b-imgs__inner">
		<div n:if="$props['progress']" class="b-imgs__progress" data-file-target="progress"></div>
		<span n:if="$props['img']" class="b-imgs__img" title="{$props['name']}">
			<img {if $props['img'] === '{imageSrc}'}data-{/if}src="{$props['img']}" alt="">
		</span>
		<button n:if="$props['dragdrop']" class="b-imgs__dragdrop btn-icon btn-icon--grab tooltip" type="button" data-drag-handle>
			{include $newTemplates.'/part/icons/grip-vertical.svg'}
			<span class="tooltip__content">
				Přesunout
			</span>
		</button>

		<div n:if="$props['btns']" class="b-imgs__btns">
			{foreach $props['btns'] as $btn}
				{var $btn = [
					icon: $btn['icon'] ?? $newTemplates.'/part/icons/user.svg',
					tooltip: $btn['tooltip'] ?? '',
					type: isset($btn['type']) && in_array($btn['type'], ['checkbox']) ? $btn['type'] : '',
					variant: isset($btn['variant']) && in_array($btn['variant'], ['remove']) ? $btn['variant'] : '',
					classes: $btn['classes'] ?? null,
					data: $btn['data'] ?? [],
				]}

				<button n:class="b-imgs__btn, btn-icon, $btn['classes'], $btn['type'] ? 'btn-icon--'.$btn['type'], $btn['variant'] ? 'btn-icon--'.$btn['variant'], $btn['tooltip'] ? 'tooltip'"
					type="button"
					{foreach $btn['data'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				>
					{include $btn['icon']}
					{if $btn['tooltip'] }
						<span class="tooltip__content">
							{$btn['tooltip']}
						</span>
					{/if}
				</button>
			{/foreach}
		</div>

		{foreach $props['inps'] as $inp}
			{var $inp = [
				input: $inp['input'],
				placeholder: $inp['placeholder'] ?? '',
				classes: $inp['classes'] ?? null,
				data: $inp['data'] ?? [],
			]}

			<span n:class="b-list__inp, $inp['classes']">
				<input class="inp-text" n:name="$inp['input']" placeholder="{$inp['placeholder']}"
					{foreach $inp['data'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				>
			</span>
		{/foreach}
		<div n:if="$props['name']" class="b-imgs__name">{$props['name']}</div>
	</div>
	<div class="block-loader__loader"></div>
</div>
