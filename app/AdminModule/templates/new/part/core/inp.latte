
{var $props = [
	input: $props['input'] ?? false,
	showLabel: $props['showLabel'] ?? true,
	labelIcon: $props['labelIcon'] ?? '',
	label: $props['label'] ?? '',
	idPrefix: $props['idPrefix'] ?? '',
	id: $props['id'] ?? '',
	name: $props['name'] ?? '',
	value: $props['value'] ?? '',
	btn: $props['btn'] ?? '',
	cols: $props['cols'] ?? 40,
	rows: $props['rows'] ?? 4,
	info: $props['info'] ?? '',
	btm: $props['btm'] ?? '',
	prefix: $props['prefix'] ?? '',
	sufix: $props['sufix'] ?? '',
	type: isset($props['type']) && in_array($props['type'], ['textarea', 'number', 'select', 'text', 'datetime-local']) ? $props['type'] : 'text',
	options: $props['options'] ?? [],
	optionsCustom: $props['optionsCustom'] ?? [],
	variant: isset($props['variant']) && in_array($props['variant'], ['h1']) ? $props['variant'] : null,
	classes: $props['classes'] ?? ['u-mb-sm'],
	classesLabel: $props['classesLabel'] ?? [''],
	multiple: $props['multiple'] ?? false,
	data: $props['data'] ?? [],
	dataInp: $props['dataInp'] ?? [],
	dataBtn: $props['dataBtn'] ?? null,
	isNew: $props['isNew'] ?? false,
]}

{var $baseClasses = ['inp']}
{if isset($props['variant'])}
	{php $baseClasses[] = 'inp--' . $props['variant']}
{/if}


{if isset($props['input']) && $props['input'] && $props['input']->hasErrors()}
	{php $baseClasses[] = 'has-error'}
{/if}


{var $classes = implode(' ', array_merge($baseClasses, $props['classes']))}
{var $classesLabel = implode(' ', array_merge(['inp-label'], $props['classesLabel']))}


<div class="{$classes}"
	{foreach $props['data'] as $key=>$value}
		data-{$key}='{$value|noescape}'
	{/foreach}
>
	{if $props['showLabel']}
		{define label}
			{if $props['labelIcon']}
				<span class="item-icon">
					<span class="item-icon__icon icon">
						{include $props['labelIcon']}
					</span>
					<span class="item-icon__text">
						{$props['input']->label->toText()}
					</span>
				</span>
			{else}
				{if $props['label']}
					{translate}{$props['label']}{/translate}
				{else}
					{$props['input']->label->toText()}
				{/if}
			{/if}
		{/define}

		{if $props['input']}
			<label n:name="$props['input']" class="{$classesLabel}">
				{include label}
			</label>
		{else}
			<label for="{$props['idPrefix']}{$props['id']}" class="{$classesLabel}">
				{include label}
			</label>
		{/if}
	{/if}
	<div class="inp-fix">
		{if $props['prefix']}
			<span class="inp-fix__prefix">
				{$props['prefix']}
			</span>
		{/if}
		{if $props['type'] == 'textarea'}
			{if $props['input']}
				<textarea
					n:name="$props['input']"
					class="inp-text"
					cols="{$props['cols']}"
					rows="{$props['rows']}"
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				></textarea>
			{else}
				<textarea
					name="{$props['name']}"
					id="{$props['idPrefix']}{$props['id']}"
					class="inp-text"
					cols="{$props['cols']}"
					rows="{$props['rows']}"
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				>{if $props['value']}{$props['value']}{/if}</textarea>
			{/if}

		{elseif $props['type'] == 'select'}
			{if $props['input']}
				<select
					n:name="$props['input']"
					class="inp-select {if $props['multiple']}inp-select--multiple{else}inp-select--simple{/if}"
					{if $props['multiple']} multiple{/if}
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
					{if $props['multiple']}data-controller="choices"{/if}
				>
				</select>
			{else}
				<select
					name="{$props['name']}"
					id="{$props['idPrefix']}{$props['id']}"
					class="inp-select {if $props['multiple']}inp-select--multiple{else}inp-select--simple{/if}"
					{if $props['multiple']} multiple{/if}
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
					{if $props['multiple']}data-controller="choices"{/if}
				>
					{if $props['options']}
						{if !$props['multiple']}
							<option value="0">
								Nezvoleno
							</option>
						{/if}
						{foreach $props['options'] as $option}
							<option value="{$option->id}" {if isset($object) && $object->getParameterById($props['id']) && $object->hasParameterValue($option)} selected="selected"{/if}>
								{$option->internalValue}
							</option>
						{/foreach}
					{elseif $props['optionsCustom']}
						{foreach $props['optionsCustom'] as $key=>$value}
							<option value="{$value['value']}"{if $value['selected'] == true} selected="selected"{/if}>{$value['text']}</option>
						{/foreach}
					{/if}
				</select>
			{/if}
		{else}
			{if $props['input']}
				<input
					n:name="$props['input']"
					class="inp-text"
					type="{$props['type']}"
					value="{if $props['input']->value == '' && $props['isNew']}{$props['value']}{else}{$props['input']->value}{/if}"
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				>
			{else}
				<input
					name="{$props['name']}"
					id="{$props['idPrefix']}{$props['id']}"
					value="{$props['value']}"
					class="inp-text"
					type="{$props['type']}"
					{foreach $props['dataInp'] as $key=>$value}
						data-{$key}="{$value|noescape}"
					{/foreach}
				>
			{/if}
		{/if}
		{if $props['sufix'] && $props['type'] == 'number'}
			<span class="inp-fix__sufix">
				{$props['sufix']}
			</span>
		{/if}
		{if $props['btn']}
			<button
				class="btn btn--grey"
				type="button"
				{foreach $props['dataBtn'] as $key=>$value }
					data-{$key}="{$value|noescape}"
				{/foreach}
			>
				<span class="btn__text">
					{$props['btn']}
				</span>
			</button>
		{/if}
		<div class="inp-text__holder"></div>
	</div>
	{if $props['info']}
		<div class="inp-info">
			{$props['info']}
		</div>
	{/if}
</div>
