{if !isset($k)} {var $k='XXX'} {/if}
{if !isset($data)} {var $data=array('key' => '', 'string' => '')} {/if}
<li>
	<div class="inner">
		<div class="grid-row">
			<p class="grid-1-5" style="width: 12%;">
				<span class="inp-fix">
					<input type="text" class="inp-text" name="new[key][{$k}]" id="inp-video{$k}" value="" data-val="" />
				</span>
			</p>
			<p class="grid-1-5" style="width: 10%;" style="align: center">

				<span class="inp-item" >
					<input type="checkbox" name="isEshop[key][{$k}]" id="inp-eshop{$k}" value="" data-val="" />
					<label for="inp-eshop{$k}"> </label>
				</span>
			</p>
			{php  $cntLang = count($langs)}

			{foreach $langs as $code=>$lang}
				<p class="grid-{if $cntLang > 2}1{else}2{/if}-5" style="width: 12%;">
					<span class="inp-fix">
						<input type="text" class="inp-text w-full" name="new[string][{$code}][{$k}]" data-value="" id="inp-video{$k}" value="" />
					</span>
				</p>
			{/foreach}
		</div>
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>
