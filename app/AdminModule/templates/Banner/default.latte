{block #content}

<div class="box-title">
	<p class="r">
		<a n:href="Banner:edit" class="btn btn-icon-before">
			<span><span class="icon icon-plus"></span> {_"New banner"}</span>
		</a>
	</p>
	<h1>{_"Banners"}</h1>
</div>
<br>
<table>
	<thead>
	<tr>
		<th>{_"id"}</th>
		<th>{_"name"}</th>
		<th>{_"public"}</th>
		<th>{_"filter_from"}</th>
		<th>{_"filter_to"}</th>
		<th>{_"label_bannerType"}</th>
		<th></th>

	</tr>
	</thead>
	<tbody>
	{foreach $banners as $banner}
		<tr class="clickable">
			<td>{$banner->id}</td>
			<td>{$banner->name}</td>
			<td>{if $banner->public}{_Yes}{else}<span class="red">ne</span>{/if}</td>
			<td>{if $banner->publicFrom}{$banner->publicFrom->format("Y-m-d H:i:s")}{/if}</td>
			<td>{if $banner->publicTo}{$banner->publicTo->format("Y-m-d H:i:s")}{/if}</td>
			<td>{$banner->nicePositionName}</td>
			<td><a n:href="edit $banner->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>

<div class="paging">
	<p class="l">
		{control pager:admin, showPages => "true"}
	</p>
	<p class="r">
		{control pager:admin, class => "r"}
	</p>
</div>
