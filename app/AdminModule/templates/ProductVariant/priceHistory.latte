{block #content}
{varType App\Model\ProductVariant $variant}
{varType Nextras\Orm\Collection\ICollection|App\Model\UserGroup[] $userGroups}
{varType App\Model\ProductVariantPriceLog[][] $prices}
{do SuperKoderi\Templating\Helpers::$mutation = $variant->product->mutation}

<h1>{_price_history_for} <a n:href="Product:edit#tab-variants id=>$variant->product->id">{$variant->product->name}</a></h1>

{foreach $userGroups as $ug}
	<h3>{$ug->name}</h3>
	<table class="table">
		<thead>
			<tr>
				<th>{_Date}</th>
				<th>{_selling_price}</th>
				<th>{_original_price}</th>
				<th>{_product_price}</th>
				<th>{_label_discountPercent}</th>
			</tr>
		</thead>
		<tbody>
			<tr n:foreach="$prices[$ug->id] as $price">
				<td>{$price->createdAt|date:'d. m. Y H:i'}</td>
				<td>{$price->salePrice|priceFormat}</td>
				<td>{$price->realOrigPrice|priceFormat}</td>
				<td>{$price->origPrice|priceFormat}</td>
				<td>{if $price->isInSale}{$price->discount} %{else}-{/if}</td>
			</tr>
		</tbody>
	</table>
{/foreach}

