{block #content}
{varType App\Model\Order $object}
{do <PERSON>Kode<PERSON>\Templating\Helpers::$mutation = $object->mutation}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}
<div class="box-title">
	<p class="r">
		<a  n:if="$isDeveloper" n:href="log $object->id" class="btn">
			<span>Log</span>
		</a>
		<a n:href="default" class="btn btn-icon-before">
			<span><span class="icon icon-arrow-left "></span> {_"Back"}</span>
		</a>
	</p>
	<h1 id="orderHeader">{_'Order'} {_'no.'} {$object->number}{if $object->isDeleted} ({_deleted}){/if}</h1>
</div>
<div class="grid-row">
	<div class="grid-1-3">
		<p>
			<span class="icon icon-globe"></span>
			<span class=""><strong>{$object->mutation->name}</strong></span><br>
			{_'Order date'}:&nbsp;<strong>{$object->created|date:'%d. %m. %Y %H:%M'}</strong><br/>
			{_'Order state'}:&nbsp;<strong class="{$object->status}">{_'order_status_'.$object->status}
				<small n:if="$object->cancelReason">({$object->cancelReason})</small></strong><br/>
			{var $paymentTypeText = $object->paymentName}
			{if App\Model\Order::isOnlinePayment($object->paymentType) && $object->subPaymentType}
				{var $paymentTypeText = 'payment_name_' .$object->subPaymentType}
			{/if}
			{_'Order payment'}:&nbsp;<strong>{_$paymentTypeText}</strong><br/>
			{if $object->paymentType == \App\Model\Order::PAYMENT_ONLINE}
				{_'Order payment status'}:&nbsp;{if $object->paymentStatus == \App\Model\Order::ONLINE_PAYMENT_PAID || $object->paymentStatus == \App\Model\Order::ONLINE_PAYMENT_PAID_NEXT_ATTEMPT}
				<span class="green"><strong>{_orderPaid}</strong></span>
			{else}
				{translate}card_{$object->paymentStatus}{/translate}
			{/if}
				<br/>
			{/if}
			{if $object->transportType == 'zasilkovna'}
				{_'Zasilkovna id'}: <strong><a target="_blank" href="https://www.zasilkovna.cz/pobocky/{$object->zasilkovnaId}">{$object->zasilkovnaId}</a></strong> <br />
				{_'Transport info text'}: <strong>{$object->transportInfoText}</strong> <br />
				<br />
			{/if}
			{if $object->transportType == 'ppl_parcel'}
				{_'PPL Parcel id'}: <strong>{$object->pplParcelCode}</strong> <br />
				{_'Transport info text'}: <strong>{$object->transportInfoText}</strong> <br />
				<br />
			{/if}
			{if isset($object->parcelNumber['baliky'][0])}
				{_'Packages'}: {foreach $object->parcelNumber['baliky'] as $package} {$package['idBalikExt']}{if !$iterator->last},{/if} {/foreach}
			{/if}
			{if isset($object->subscriptionOrder)}
				{_'Subscription order'}: <strong><a n:href="Subscription:edit $object->subscriptionOrder->subscription->id">{$object->subscriptionOrder->id}</a></strong><br>
			{_'Subscription'}: <strong><a n:href="Subscription:edit $object->subscriptionOrder->subscription->id">{$object->subscriptionOrder->subscription->name}</a></strong><br>
			{/if}
		</p>
		<p n:if="$object->hasInvoice">
			<a n:href="invoicePdf!" class="btn btn-icon-before">
				<span><span class="icon icon-file"></span>Invoice PDF</span>
			</a>
		</p>
		<p n:if="$object->hasInvoice">
			<a n:href="invoiceXML!" class="btn btn-icon-before">
				<span><span class="icon icon-file"></span>Invoice XML</span>
			</a>
		</p>
		<p n:if="$object->hasCreditNote">
			<a n:foreach="$object->creditNotes as $creditNote" n:href="creditNotePdf! $creditNote->id " class="btn btn-icon-before">
				<span><span class="icon icon-file"></span>Credit note PDF</span>
			</a>
		</p>
		<p n:if="$object->hasCreditNote">
			<a n:foreach="$object->creditNotes as $creditNote" n:href="creditNoteXML! $creditNote->id " class="btn btn-icon-before">
				<span><span class="icon icon-file"></span>Credit note XML</span>
			</a>
		</p>
		<br/>
	</div>
{if $object->novikoId}
	<div class="grid-1-3" n:if="$object->novikoId">
		<h3>{_order_noviko_section}</h3>
		<p>
			ID: <strong>{_$object->novikoId}</strong> | HD ID:
			<strong n:if="$object->novikoHdId">{_$object->novikoHdId}</strong><br>
			{_order_noviko_status}: <strong>{_$object->novikoStatusName} ({$object->novikoStatus})</strong><br>
			{_order_noviko_last_update}: <strong>{$object->novikoUpdated|date:'%d. %m. %Y %H:%M:%S'}</strong>
		</p>
		<p>
			<a n:if="$object->novikoId && $object->status != App\Model\Order::STATUS_CANCEL"
					class="btn" n:href="novikoUpdate!">
				<span>Update status</span>
			</a>
		</p>
	</div>
{else}
	<div class="grid-1-3">
		<p>
			{if isset($user) && ($user->isAdmin() || $user->isDeveloper())}
			<a n:if="$object->isCanRevokeStorno"
						class="btn btn-warning" n:href="revokeStorno!">
				<span>Revoke storno</span>
			</a>
			{/if}
		</p>
	</div>
{/if}
	<div class="grid-1-3">
		{if $object->isCanStorno}
			{if isset($user) && ($user->isAdmin() || $user->isDeveloper())}
				{form stornoForm}
					<div class="grid-row">

						<p class="grid-4-5">
							{label cancelReason /}<br>
							<button class="btn btn-red btn-warning" style="float:right">
								<span>Storno</span>
							</button>
							<span class="inp-fix inp-fix-select">
								{input cancelReason class => 'inp-text'}
							</span>
						</p>
						{*
						<a n:if="$object->status != App\Model\Order::STATUS_CANCEL" class="btn btn-red btn-warning ajax" n:href="storno!">
							<span>Storno</span>
						</a>
						 *}
					</div>
				{/form}
			{/if}
		{/if}

		{if $isDeveloper && empty($object->mailConfirmationSend)}
			<h3>{_order_detail_email_not_sent_yet}</h3>
			<a n:href="reSendOrderMail! $object->id">
				<button class="btn btn-red btn-warning" style="float:left">
					<span>{_resend_order_detail_email}</span>
				</button>
			</a>
		{else}
			<h3>{_order_detail_email_sent}</h3>
		{/if}
		<p>
			{if $object->mailConfirmationSend}{_order_email_sent_confirm}: {$object->mailConfirmationSend|date:'%d. %m. %Y %H:%M:%S'}
				<br>{/if}
			{if $object->mailSent}{_order_email_sent_shipped}: {$object->mailSent|date:'%d. %m. %Y %H:%M:%S'}<br>{/if}
			{if $object->mailStorno}{_order_email_sent_storno}: {$object->mailStorno|date:'%d. %m. %Y %H:%M:%S'}<br>{/if}
			{if $object->mailStornoWarehouse}{_order_email_sent_storno_warehouse}: {$object->mailStornoWarehouse|date:'%d. %m. %Y %H:%M:%S'}{/if}
		</p>
		<br>
	</div>
</div>

{form editForm}
	<div class="grid-row">

		{*
		<p class="grid-1-3">
			{label status /}<br>
			<button class="btn btn-green btn-icon-before r">
				<span><span class="icon icon-checkmark"></span> {_change_status_button}</span>
			</button>

			<span class="inp-fix  inp-fix-select">
				{input status class => 'inp-text w-full'}
			</span>

			{*label barCode /}
			<span class="inp-fix">
				{input barCode class => 'inp-text'}
			</span>*
		</p>
	 *}



		{*if $object->status != 'cancel'}

			{if $object->transportType == 'personal'}

				{if $object->status == 'progress'}
					<p class="grid-1-5">
						<label>{_order_detail_inprogress}:</label><br />
						{if $object->mailProgress != '0000-00-00 00:00:00' && $object->mailProgress !== NULL}
							{_order_detail_email_sent}: <strong>{$object->mailProgress|date:'%d. %m. %Y %H:%M'}</strong>
						{/if}
						<a n:href="Order:sendMailProgress $object->id" class="btn btn-warning"
						   title='{_order_detail_info_email_tooltip2}'>
							<span>{_order_detail_send_email}</span>
						</a>
						<br>
					</p>
				{/if}

				<p class="grid-1-5">
					<label>{_order_detail_ready}:</label><br />
					{if $object->mailPrepared != '0000-00-00 00:00:00' && $object->mailPrepared !== NULL}
						{_order_detail_email_sent}: <strong>{$object->mailPrepared|date:'%d. %m. %Y %H:%M'}</strong>
					{/if}
					<a n:href="Order:sendMailPrepared $object->id" class="btn btn-warning"
					   title='{_order_detail_info_email_tooltip}'>
						<span>{_order_detail_send_email}</span>
					</a>
					<br>
					<span class="small">{_order_detail_change_status} <strong>{_order_status_ready}</strong></span>

				</p>
			{/if}


			{if $object->transportType == 'ppl' || $object->transportType == 'pplEurope' || $object->transportType == 'pplSk' || $object->transportType == 'post' || $object->transportType == 'posteRestante'}

				{if $object->status == 'progress'}
					<p class="grid-1-5">
						<label>{_order_detail_inprogress}:</label><br />
						{if $object->mailProgress != '0000-00-00 00:00:00' && $object->mailProgress !== NULL}
							E-mail odeslán: <strong>{$object->mailProgress|date:'%d. %m. %Y %H:%M'}</strong>
						{/if}
						<a n:href="Order:sendMailProgress $object->id" class="btn btn-warning"
						   title='{_order_detail_info_email_tooltip2}'>
							<span>{_order_detail_send_email}</span>
						</a>
						<br>
					</p>
				{/if}

				<p class="grid-1-5">
					<label>{_order_detail_sent}:</label><br />
					{if $object->mailSent != '0000-00-00 00:00:00' && $object->mailSent !== NULL}
						{_order_detail_email_sent}: <strong>{$object->mailSent|date:'%d. %m. %Y %H:%M'}</strong>
					{/if}
					<a n:href="Order:sendMailSent $object->id" class="btn btn-warning"
					   title='{_order_detail_info_email_tooltip3}'>
						<span>{_order_detail_send_email}</span>
					</a>
					<br>
					<span class="small">{_order_detail_change_status} <strong>{_order_status_done}</strong></span>

					<br>
					<i class="small">{_order_detail_info_email_tooltip4}</i>

				</p>
			{/if}

		{/if}

		<p class="grid-1-5" n:if="$object->status == 'cancel'">
			<label>{_order_detail_storno}:</label><br />

			{if $object->mailStorno}
				{_order_detail_email_sent}: <strong>{$object->mailStorno|date:'%d. %m. %Y %H:%M'}</strong>
			{else}
				<a n:href="Order:sendMailStorno $object->id" class="btn btn-warning" title='{_order_detail_info_email_tooltip5}'>
					<span>{_order_detail_send_email}</span>
				</a>
			{/if}
		</p>



		---------


		{if $object->transportType == 'personal'}
		<p class="grid-1-5">
			<label>Objednávka připravena:</label><br />
			{if $object->mailPrepared != '0000-00-00 00:00:00'}
				E-mail odeslán: <strong>{$object->mailPrepared|date:'%d. %m. %Y %H:%M'}</strong>
			{/if}
				<a n:href="Order:sendMailPrepared $object->id" class="btn"
				   title='Odešle informační e-mail, že objednávka byla zpracována a je připravena k vyzvednutí / k odeslání.
				   Zároveň nastaví stav na "Připravena"'>
					<span>Odeslat infomail zákazníkovi</span>
				</a>

		</p>
		{/if}

		{if $object->transportType == 'ppl' || $object->transportType == 'toptrans' || $object->transportType == 'pplSk' || $object->transportType == 'post' || $object->transportType == 'posteRestante'}
			<p class="grid-1-5">
				<label>Objednávka odeslána:</label><br />
				{if $object->mailSent != '0000-00-00 00:00:00'}
					E-mail odeslán: <strong>{$object->mailSent|date:'%d. %m. %Y %H:%M'}</strong>
				{/if}
					<a n:href="Order:sendMailSent $object->id" class="btn"
					   title='Odešle informační e-mail, že objednávka byla odeslána. Zároveň nastaví stav na "Vyřízená"'>
						<span>Odeslat infomail zákazníkovi</span>
					</a>

			</p>
		{/if}

		<p class="grid-1-5" n:if="$object->status == 'cancel'">
			<label>Objednávka stronována:</label><br />

			{if $object->mailStorno}
				E-mail odeslán: <strong>{$object->mailStorno|date:'%d. %m. %Y %H:%M'}</strong>
			{else}
				<a n:href="Order:sendMailStorno $object->id" class="btn" title='Odešle informační e-mail, že objednávka byla zrušena. Zároveň nastaví stav na "Storno"'>
					<span>Odeslat infomail zákazníkovi</span>
				</a>
			{/if}
		</p>
		*}

	</div>

	<div class="grid-row box-detail-table">
		{var $readonly = !($user->isAdmin() || $user->isDeveloper())}
		<div class="grid-1-3">
			<h2>{_'Billing address'}</h2>

			<div class="grid-row">
				<p class="grid-1-2">
					{label firstname /}
					<span class="inp-fix">
					{input firstname class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label lastname /}
					<span class="inp-fix">
					{input lastname class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label email /}
					<span class="inp-fix">
					{input email class => 'inp-text', disabled => $readonly}
				</span>
				</p>
				<p class="grid-1-2">
					{label phone /}
					<span class="inp-fix">
					{input phone class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label street /}
					<span class="inp-fix">
					{input street class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label city /}
					<span class="inp-fix">
					{input city class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label zip /}
					<span class="inp-fix">
					{input zip class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label state /}
					<span class="inp-fix inp-fix-select">
					{input state class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label infotext /}
					<span class="inp-fix">
					{input infotext class => 'inp-text', disabled => $readonly}
				</span>
				</p>
			</div>

		</div>
		<div class="grid-1-3">
			<h2>{_'Corporate information'}</h2>

			<div class="grid-row">
				<p class="grid-1">
					{label company /}
					<span class="inp-fix">
					{input company class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label ic /}
					<span class="inp-fix">
					{input ic class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label dic /}
					<span class="inp-fix">
					{input dic class => 'inp-text', disabled => $readonly}
				</span>
				</p>
			</div>

		</div>
		<div class="grid-1-3">
			<h2>{_'Delivery address'}</h2>

			<div class="grid-row">

				<p class="grid-1-2">
					{label dFirstname /}
					<span class="inp-fix">
					{input dFirstname class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label dLastname /}
					<span class="inp-fix">
					{input dLastname class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1-2">
					{label dCompany /}
					<span class="inp-fix">
					{input dCompany class => 'inp-text', disabled => $readonly}
				</span>
				</p>
				<p class="grid-1-2">
					{label dPhone /}
					<span class="inp-fix">
					{input dPhone class => 'inp-text', disabled => $readonly}
				</span>
				</p>

				<p class="grid-1">
					{label dStreet /}
					<span class="inp-fix">
					{input dStreet class => 'inp-text', disabled => $readonly}
				</span>
				</p>
				<p class="grid-1-2">
					{label dCity /}
					<span class="inp-fix">
					{input dCity class => 'inp-text', disabled => $readonly}
				</span>
				</p>
				<p class="grid-1-2">
					{label dZip /}
					<span class="inp-fix">
					{input dZip class => 'inp-text', disabled => $readonly}
				</span>
				</p>


				<p class="grid-1">
					{label dState /}
					<span class="inp-fix inp-fix-select">
					{input dState class => 'inp-text', disabled => $readonly}
				</span>
				</p>


				<p class="grid-1">
					{label dInfo /}
					<span class="inp-fix">
					{input dInfo class => 'inp-text', disabled => $readonly}
				</span>
				</p>
			</div>
			{if !$readonly}
				<button class="btn btn-green btn-icon-before r">
					<span><span class="icon icon-checkmark"></span> {_save_button}</span>
				</button>
			{/if}
		</div>
	</div>

{/form}

{*
<div class="grid-row box-detail-table">
	<div class="grid-1-3">
		<h2>{_'Billing address'}</h2>
		<table class="reset">
			<tr>
				<td class="right bold">{_'user_name'}</td>
				<td>{$object->name}</td>
			</tr>
			<tr>
				<td class="right bold">{_'email'}</td>
				<td>{$object->email}</td>
			</tr>
			<tr>
				<td class="right bold">{_'phone'}</td>
				<td>{if $object->phone}{$object->phone}{else}&mdash;{/if}</td>
			</tr>
			<tr>
				<td class="right bold">{_'address'}</td>
				<td>{$object->street}, {$object->city} {$object->zip}</td>
			</tr>
			<tr>
				<td class="right bold">{_'Note'}</td>
				<td>{if $object->infotext}{$object->infotext}{else}&mdash;{/if}</td>
			</tr>
		</table>
	</div>
	<div class="grid-1-3">
		<h2>{_'Corporate information'}</h2>
		<table class="reset">
			<tr>
				<td class="right bold">{_'company_id'}</td>
				<td>{if $object->ic}{$object->ic}{else}&mdash;{/if}</td>
			</tr>
			<tr>
				<td class="right bold">{_'vat_number'}</td>
				<td>{if $object->dic}{$object->dic}{else}&mdash;{/if}</td>
			</tr>
		</table>
	</div>
	<div class="grid-1-3">
		<h2>{_'Delivery address'}</h2>
		<table class="reset">
			<tr>
				<td class="right bold">{_'user_name'}</td>
				<td>{$object->dName}</td>
			</tr>
			<tr>
				<td class="right bold">{_'company'}</td>
				<td>{if $object->dCompany}{$object->dCompany}{else}&mdash;{/if}</td>
			</tr>
			<tr>
				<td class="right bold">{_'address'}</td>
				<td>{$object->dStreet}, {$object->dCity} {$object->dZip}</td>
			</tr>
			<tr>
				<td class="right bold">{_'info'}</td>
				<td>{if $object->dInfo}{$object->dInfo}{else}&mdash;{/if}</td>
			</tr>
		</table>
	</div>
</div>
*}

<h2 id="orderItem">{_'Ordered goods'}</h2>

{form creditNoteForm}
	<ul class="message message-error" n:if="$form->hasErrors()">
		<li n:foreach="$form->errors as $error">{$error}</li>
	</ul>
	{php $isVoucher = FALSE}
	{foreach $object->items as $i}
		{if $i->type == 'product' && ($i->subType=='voucher' || $i->subType=='voucherElectronic') && $i->variantName==""}
			{php $isVoucher = TRUE}
		{/if}
	{/foreach}

	{if $isVoucher}
		<a n:href="generateVoucher! orderId=>$object->id" class="btn">
	<span>
			Vygenerovat slevové kódy u zakoupených poukazů
	</span>
		</a>
		<br>
		<br>
	{/if}
	<table>
		<thead>
		<tr>
			<th>{_product}</th>
			<th n:if="$i->order->subscriptionOrder">{_subscriptionOrderItemType}</th>
			<th>{_amount}</th>
			<th class="right">{_price}</th>
			<th class="right">{_price_vat}</th>
			<th style="width:200px">{if $canCreateCreditNote}{_credit_note_amount}{/if}</th>
		</tr>
		</thead>

		<tbody>
		{foreach $object->items as $i}
			<tr>
				<td>
					{if $i->type == 'product' && $i->subType=='voucher'}
						<strong>Poukázka:</strong>
					{/if}

					{if $i->type == 'sample' }
						<strong>[sample]</strong>
					{/if}

					{if $i->type == 'present' }
						<strong>[present]</strong>
					{/if}

					{if $i->type == 'gift' }
						<strong>[gift]</strong>
					{/if}

					{if $i->type == 'voucher'}
						<strong>[voucher]</strong>
					{/if}

					{var $itemLink = false}
					{var $voucherCode = false}
					{if $i->type === 'product' && $i->productId}
						{var $itemLink = ['Product:edit', $i->productId]}
					{elseif $i->type === 'voucher'}
						{var $voucherCode = $i->voucherCode}
						{if $voucherCode}
							{var $itemLink = ['Voucher:edit', $voucherCode->voucher->id]}
						{/if}
					{/if}

					<a n:href="$itemLink[0],id=>$itemLink[1]" n:tag-if="$itemLink">
						{$i->name}
					</a>
					{if $i->type == 'product' && ($i->subType=='voucher' || $i->subType=='voucherElectronic')}
						<br>
						kód(y):
						{if $i->variantName}
							<span class="">{$i->variantName}</span>
							<br>
							{if !$i->voucherSentTime}
								<a n:href="sendVoucherToEmail! orderItemId=>$i->id" class="btn">
									<span>Odeslat na e-mail</span>
								</a>
							{else}
								<span class="green">odesláno</span>,
								<a n:href="sendVoucherToEmail! orderItemId=>$i->id" class="">
									<span> odeslat znovu</span>
								</a>
							{/if}

							| <a n:href="makePdf! orderItemId=>$i->id" target="_blank" class="red">
							<span> stáhnout v pdf</span>
						</a>
						{else}
							nejsou zatím vygenerovány
						{/if}
					{else}
						{if $i->variantName}<span class="small">({$i->variantName})</span>{/if}
					{/if}


					{if $i->type == 'voucher' && $voucherCode}
						<span class="small">{$voucherCode->code}</span>
					{/if}

				</td>
				<td n:if="$i->order->subscriptionOrder">
					{if $i->type == 'subscription'}
						subscription
					{elseif $i->type == 'product'}
						one-time
					{/if}
				</td>
				<td>{$i->amount} pcs</td>
				<td class="right">
					{$i->amount} &times;
					{if $object->mutation->langCode == 'sk'}
						{$i->unitPrice|priceFormat:true, 3}
					{else}
						{$i->unitPrice|priceFormat:true}
					{/if}
				</td>
				<td class="right">
					{$i->amount} &times;
					{$i->unitPriceDPH|priceFormat}
				</td>
				<td>
					{if isset($user) && ($user->isAdmin() || $user->isDeveloper())}
						{if $canCreateCreditNote && isset($form[$i->id])}
							{formContainer $i->id}
								<span class="inp-fix inp-fix-select">
								{input amount class => 'inp-text'}
							</span>
							{/formContainer}
						{/if}
					{/if}
				</td>
			</tr>
		{/foreach}
		</tbody>
		<tfoot>
		<tr>
			<td colspan="2">{_price_sum}:</td>
			<td n:if="$i->order->subscriptionOrder">
			</td>
			<td class="right">
				{$object->totalPrice|priceFormat:true}
			</td>
			<td class="right">
				{$object->totalPriceDPH|priceFormat}
			</td>
			<td></td>
		</tr>
		<tr n:if="$canCreateCreditNote && ($user->isAdmin() || $user->isDeveloper())">
			<td colspan="2" class="right">
				{_credit_note_cancel_reason}
			</td>
			<td colspan="2" class="right">
				<span class="inp-fix inp-fix-select">
					{input cancelReason class => 'inp-text'}
				</span>
			</td>
			<td class="right">
				<button class="btn btn-green btn-warning" style="float:right">
					<span>{_credit_note_btn_create}</span>
				</button>
			</td>
		</tr>
		</tfoot>
	</table>
	<br><br>
{/form}


{*
<p class="right" n:if="$object->status == \App\Model\Order::STATUS_CANCEL && $object->isDeleted == 0">
	<a class="btn btn-red btn-icon-before btn-delete" n:href="deleteOrder!">
		<span><span class="icon icon-close"></span> {_delete_button}</span>
	</a>
</p>
*}

{*
DEBUG UPLOADIFY
{form uploadForm}
	{input file_upload}
	{input save}
{/form}
*}
