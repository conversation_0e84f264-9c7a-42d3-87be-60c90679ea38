<div class="" n:ifset="$statistics">
	<a n:ifset="$statistics" {if count($orders) > 0}href="{plink statistics! 0}"{/if} n:class="btn, btn-blue, btn-icon-before, count($orders) === 0 ? btn-disabled">
		<span><span class="icon icon-eye-blocked"></span> {_order_statistics_hide}</span>
	</a>
	{foreach $statistics as $mKey => $mutStatistics}
		<p>
			<h2>{_mutation}: {$mKey}</h2>
			{if isset($mutStatistics['summary']) && count($mutStatistics['summary']) > 0}
				<table style="width: 270px;">
					<th>{_invoice_count}</th>
					<th>{_credit_note_count}</th>
					<tr>
					{if isset($mutStatistics['invoices'])}
						<td>{$mutStatistics['invoices']}</td>
					{else}
						<td>0</td>
					{/if}
					{if isset($mutStatistics['creditNotes'])}
						<td>{$mutStatistics['creditNotes']}</td>
					{else}
						<td>0</td>
					{/if}
					</tr>
				</table>
				<table style="width: 540px;">
					<th>{_vat_rate}</th>
					<th>{_price_vat}</th>
					<th>{_price}</th>
					<th>{_vat}</th>
					{do SuperKoderi\Templating\Helpers::$mutation = $mutStatistics['mutation']}
					{foreach $mutStatistics['summary'] as $summaryKey => $summary}
						<tr>
							<td>{$summaryKey} %</td>
							<td>{$summary['priceDPH']|priceFormat:true}</td>
							<td>{$summary['price']|priceFormat:true}</td>
							<td>{$summary['DPH']|priceFormat:true}</td>
						</tr>
					{/foreach}
				</table>
			{/if}
		</p>
	{/foreach}
</div>
