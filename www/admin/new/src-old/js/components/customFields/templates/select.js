export const select = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	parent.append(tplElement);
	const values = value.split(',');
	tplElement.outerHTML = `
<div class="inp u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'>
	${scheme.label ? `<label class="inp-label title" for="custom-${key}">${scheme.label}</label>` : ''}
	<div class="inp-fix">
		<select name="custom-${key}" id="custom-${key}" class="inp-select" data-controller="Select2" data-select2-target="item" data-action="change->CustomField#${
		scheme.multiple ? 'updateMultiselectValue' : 'updateValue'
	}"${scheme.multiple ? ' multiple' : ''}>
			${!scheme.multiple && !scheme.hideNoValueOption ? '<option value="0">Nezvoleno</option>' : ''}
			${scheme.options
				.map(
					(option) =>
						`<option value="${option.value}"${values.includes(option.value) ? ' selected' : ''}>${option.label}</option>`,
				)
				.join('')}
		</select>
		<div class="inp-text__holder"></div>
	</div>
</div>
`;
};
