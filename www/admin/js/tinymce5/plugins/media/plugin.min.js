/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(){"use strict";var a,e,t,r,n,i=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},v=o("string"),c=o("function"),u=Object.prototype.hasOwnProperty,b=(a=function(e,t){return t},function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var r={},n=0;n<e.length;n++){var i=e[n];for(var o in i)u.call(i,o)&&(r[o]=a(r[o],i[o]))}return r}),s=function(e){return function(){return e}},l=s(!1),m=s(!0),d=l,h=m,f=function(){return p},p=(n={fold:function(e,t){return e()},is:d,isSome:d,isNone:h,getOr:r=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:r,orThunk:t,map:f,ap:f,each:function(){},bind:f,flatten:f,exists:d,forall:h,filter:f,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:s("none()")},Object.freeze&&Object.freeze(n),n),g=function(r){var e=function(){return r},t=function(){return i},n=function(e){return e(r)},i={fold:function(e,t){return t(r)},is:function(e){return r===e},isSome:h,isNone:d,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return g(e(r))},ap:function(e){return e.fold(f,function(e){return g(e(r))})},each:function(e){e(r)},bind:n,flatten:e,exists:n,forall:n,filter:function(e){return e(r)?i:p},equals:function(e){return e.is(r)},equals_:function(e,t){return e.fold(d,function(e){return t(r,e)})},toArray:function(){return[r]},toString:function(){return"some("+r+")"}};return i},w={some:g,none:f,from:function(e){return null===e||e===undefined?p:g(e)}},y=Object.hasOwnProperty,x=function(e,t){return j(e,t)?w.some(e[t]):w.none()},j=function(e,t){return y.call(e,t)},O=Array.prototype.push,A=(Array.prototype.slice,c(Array.from)&&Array.from,tinymce.util.Tools.resolve("tinymce.util.Tools")),_=function(e){return e.getParam("media_scripts")},C=function(e){return e.getParam("audio_template_callback")},M=function(e){return e.getParam("video_template_callback")},F=function(e){return e.getParam("media_live_embeds",!0)},P=function(e){return e.getParam("media_filter_html",!0)},S=function(e){return e.getParam("media_url_resolver")},k=function(e){return e.getParam("media_alt_source",!0)},T=function(e){return e.getParam("media_poster",!0)},$=function(e){return e.getParam("media_dimensions",!0)},z=tinymce.util.Tools.resolve("tinymce.html.SaxParser"),D=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),N=function(e,t){if(e)for(var r=0;r<e.length;r++)if(-1!==t.indexOf(e[r].filter))return e[r]},E=function(t){return function(e){return e?e.style[t].replace(/px$/,""):""}},U=function(n){return function(e,t){var r;e&&(e.style[n]=/^[0-9.]+$/.test(r=t)?r+"px":r)}},R={getMaxWidth:E("maxWidth"),getMaxHeight:E("maxHeight"),setMaxWidth:U("maxWidth"),setMaxHeight:U("maxHeight")},W=D.DOM,H=function(e){return W.getAttrib(e,"data-ephox-embed-iri")},L=function(e,t){return c=t,u=W.createFragment(c),""!==H(u.firstChild)?(o=t,a=W.createFragment(o).firstChild,{type:"ephox-embed-iri",source1:H(a),source2:"",poster:"",width:R.getMaxWidth(a),height:R.getMaxHeight(a)}):(n=e,r=t,z({validate:(i={},!1),allow_conditional_comments:!0,special:"script,noscript",start:function(e,t){if(i.source1||"param"!==e||(i.source1=t.map.movie),"iframe"!==e&&"object"!==e&&"embed"!==e&&"video"!==e&&"audio"!==e||(i.type||(i.type=e),i=A.extend(t.map,i)),"script"===e){var r=N(n,t.map.src);if(!r)return;i={type:"script",source1:t.map.src,width:r.width,height:r.height}}"source"===e&&(i.source1?i.source2||(i.source2=t.map.src):i.source1=t.map.src),"img"!==e||i.poster||(i.poster=t.map.src)}}).parse(r),i.source1=i.source1||i.src||i.data,i.source2=i.source2||"",i.poster=i.poster||"",i);var n,r,i,o,a,c,u},I=tinymce.util.Tools.resolve("tinymce.util.Promise"),q=function(e){var t={mp3:"audio/mpeg",wav:"audio/wav",mp4:"video/mp4",webm:"video/webm",ogg:"video/ogg",swf:"application/x-shockwave-flash"}[e.toLowerCase().split(".").pop()];return t||""},B=tinymce.util.Tools.resolve("tinymce.html.Writer"),G=tinymce.util.Tools.resolve("tinymce.html.Schema"),J=D.DOM,K=function(e,t){var r,n,i,o;for(r in t)if(i=""+t[r],e.map[r])for(n=e.length;n--;)(o=e[n]).name===r&&(i?(e.map[r]=i,o.value=i):(delete e.map[r],e.splice(n,1)));else i&&(e.push({name:r,value:i}),e.map[r]=i)},Q=function(e,t){var r,n,i=J.createFragment(e).firstChild;return R.setMaxWidth(i,t.width),R.setMaxHeight(i,t.height),r=i.outerHTML,n=B(),z(n).parse(r),n.getContent()},V=function(e,t,r){return s=e,l=J.createFragment(s),""!==J.getAttrib(l.firstChild,"data-ephox-embed-iri")?Q(e,t):(n=e,i=t,o=r,c=B(),z({validate:!1,allow_conditional_comments:!(u=0),special:"script,noscript",comment:function(e){c.comment(e)},cdata:function(e){c.cdata(e)},text:function(e,t){c.text(e,t)},start:function(e,t,r){switch(e){case"video":case"object":case"embed":case"img":case"iframe":i.height!==undefined&&i.width!==undefined&&K(t,{width:i.width,height:i.height})}if(o)switch(e){case"video":K(t,{poster:i.poster,src:""}),i.source2&&K(t,{src:""});break;case"iframe":K(t,{src:i.source1});break;case"source":if(++u<=2&&(K(t,{src:i["source"+u],type:i["source"+u+"mime"]}),!i["source"+u]))return;break;case"img":if(!i.poster)return;a=!0}c.start(e,t,r)},end:function(e){if("video"===e&&o)for(var t=1;t<=2;t++)if(i["source"+t]){var r=[];r.map={},u<t&&(K(r,{src:i["source"+t],type:i["source"+t+"mime"]}),c.start("source",r,!0))}if(i.poster&&"object"===e&&o&&!a){var n=[];n.map={},K(n,{src:i.poster,width:i.width,height:i.height}),c.start("img",n,!0)}c.end(e)}},G({})).parse(n),c.getContent());var n,i,o,a,c,u,s,l},X=[{regex:/youtu\.be\/([\w\-_\?&=.]+)/i,type:"iframe",w:560,h:314,url:"//www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/youtube\.com(.+)v=([^&]+)(&([a-z0-9&=\-_]+))?/i,type:"iframe",w:560,h:314,url:"//www.youtube.com/embed/$2?$4",allowFullscreen:!0},{regex:/youtube.com\/embed\/([a-z0-9\?&=\-_]+)/i,type:"iframe",w:560,h:314,url:"//www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/vimeo\.com\/([0-9]+)/,type:"iframe",w:425,h:350,url:"//player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc",allowFullscreen:!0},{regex:/vimeo\.com\/(.*)\/([0-9]+)/,type:"iframe",w:425,h:350,url:"//player.vimeo.com/video/$2?title=0&amp;byline=0",allowFullscreen:!0},{regex:/maps\.google\.([a-z]{2,3})\/maps\/(.+)msid=(.+)/,type:"iframe",w:425,h:350,url:'//maps.google.com/maps/ms?msid=$2&output=embed"',allowFullscreen:!1},{regex:/dailymotion\.com\/video\/([^_]+)/,type:"iframe",w:480,h:270,url:"//www.dailymotion.com/embed/video/$1",allowFullscreen:!0},{regex:/dai\.ly\/([^_]+)/,type:"iframe",w:480,h:270,url:"//www.dailymotion.com/embed/video/$1",allowFullscreen:!0}],Y=function(r,e){var n=A.extend({},e);if(!n.source1&&(A.extend(n,L(_(r),n.embed)),!n.source1))return"";n.source2||(n.source2=""),n.poster||(n.poster=""),n.source1=r.convertURL(n.source1,"source"),n.source2=r.convertURL(n.source2,"source"),n.source1mime=q(n.source1),n.source2mime=q(n.source2),n.poster=r.convertURL(n.poster,"poster");var t,i,o=(t=n.source1,0<(i=X.filter(function(e){return e.regex.test(t)})).length?A.extend({},i[0],{url:function(e,t){for(var r=e.regex.exec(t),n=e.url,i=function(e){n=n.replace("$"+e,function(){return r[e]?r[e]:""})},o=0;o<r.length;o++)i(o);return n.replace(/\?$/,"")}(i[0],t)}):null);if(o&&(n.source1=o.url,n.type=o.type,n.allowFullscreen=o.allowFullscreen,n.width=n.width||o.w,n.height=n.height||o.h),n.embed)return V(n.embed,n,!0);var a=N(_(r),n.source1);a&&(n.type="script",n.width=a.width,n.height=a.height);var c,u,s,l,m,d,h,f,p=C(r),g=M(r);return n.width=n.width||300,n.height=n.height||150,A.each(n,function(e,t){n[t]=r.dom.encode(e)}),"iframe"===n.type?(f=(h=n).allowFullscreen?' allowFullscreen="1"':"",'<iframe src="'+h.source1+'" width="'+h.width+'" height="'+h.height+'"'+f+"></iframe>"):"application/x-shockwave-flash"===n.source1mime?(d='<object data="'+(m=n).source1+'" width="'+m.width+'" height="'+m.height+'" type="application/x-shockwave-flash">',m.poster&&(d+='<img src="'+m.poster+'" width="'+m.width+'" height="'+m.height+'" />'),d+="</object>"):-1!==n.source1mime.indexOf("audio")?(s=n,(l=p)?l(s):'<audio controls="controls" src="'+s.source1+'">'+(s.source2?'\n<source src="'+s.source2+'"'+(s.source2mime?' type="'+s.source2mime+'"':"")+" />\n":"")+"</audio>"):"script"===n.type?'<script src="'+n.source1+'"><\/script>':(c=n,(u=g)?u(c):'<video width="'+c.width+'" height="'+c.height+'"'+(c.poster?' poster="'+c.poster+'"':"")+' controls="controls">\n<source src="'+c.source1+'"'+(c.source1mime?' type="'+c.source1mime+'"':"")+" />\n"+(c.source2?'<source src="'+c.source2+'"'+(c.source2mime?' type="'+c.source2mime+'"':"")+" />\n":"")+"</video>")},Z={},ee=function(t){return function(e){return Y(t,e)}},te=function(e,t){var r,n,i,o,a,c=S(e);return c?(i=t,o=ee(e),a=c,new I(function(t,e){var r=function(e){return e.html&&(Z[i.source1]=e),t({url:i.source1,html:e.html?e.html:o(i)})};Z[i.source1]?r(Z[i.source1]):a({url:i.source1},r,e)})):(r=t,n=ee(e),new I(function(e){e({html:n(r),url:r.source1})}))},re=function(e){return Z.hasOwnProperty(e)},ne=function(e){return b(e,{source1:e.source1.value,source2:x(e,"source2").bind(function(e){return x(e,"value")}).getOr(""),poster:x(e,"poster").bind(function(e){return x(e,"value")}).getOr("")})},ie=function(e){return b(e,{source1:{value:x(e,"source1").getOr("")},source2:{value:x(e,"source2").getOr("")},poster:{value:x(e,"poster").getOr("")}})},oe=function(r){return function(e){var t=e&&e.msg?"Media embed handler error: "+e.msg:"Media embed handler threw unknown error.";r.notificationManager.open({type:"error",text:t})}},ae=function(e,t){return A.extend({},L(_(e),t))},ce=function(e,t){var r=e.dom.select("img[data-mce-object]");e.insertContent(t),function(e,t){var r,n,i=e.dom.select("img[data-mce-object]");for(r=0;r<t.length;r++)for(n=i.length-1;0<=n;n--)t[r]===i[n]&&i.splice(n,1);e.selection.select(i[0])}(e,r),e.nodeChanged()},ue=function(a){var e,t,r,n,i,o=(t=(e=a).selection.getNode(),(r=t.getAttribute("data-ephox-embed-iri"))?{source1:r,width:R.getMaxWidth(t),height:R.getMaxHeight(t)}:t.getAttribute("data-mce-object")?L(_(e),e.serializer.serialize(t,{selection:!0})):{}),c={source1:"",source2:"",embed:(n=a,i=n.selection.getNode(),i.getAttribute("data-mce-object")||i.getAttribute("data-ephox-embed-iri")?n.selection.getContent():""),poster:"",dimensions:{height:o.height?o.height:"",width:o.width?o.width:""}},u=ie(b(c,o)),s=function(e){var t=ne(e.getData());return $(a)?b(t,{width:t.dimensions.width,height:t.dimensions.height}):t},l=function(e){var r,o,t=s(e);te(a,t).then((r=w,o=a,function(e){if(v(e.url)&&0<e.url.trim().length){var t=e.html,n=ae(o,t),i={source1:e.url,embed:t};!function(e,t){for(var r=0,n=e.length;r<n;r++)t(e[r],r,e)}(["width","height"],function(r){x(n,r).each(function(e){var t=i.dimensions||{};t[r]=e,i.dimensions=t})}),r.setData(ie(i))}}))["catch"](oe(a))},m={title:"General",items:function(e){for(var t=[],r=0,n=e.length;r<n;++r){if(!Array.prototype.isPrototypeOf(e[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+e);O.apply(t,e[r])}return t}([[{name:"source1",type:"urlinput",filetype:"media",label:"Source"}],$(a)?[{type:"sizeinput",name:"dimensions",label:"Constrain proportions",constrain:!0}]:[]])},d={title:"Embed",items:[{type:"textarea",name:"embed",label:"Paste your embed code below:"}]},h=[];k(a)&&h.push({name:"source2",type:"urlinput",filetype:"media",label:"Alternative source URL"}),T(a)&&h.push({name:"poster",type:"urlinput",filetype:"image",label:"Media poster (Image URL)"});var f={title:"Advanced",items:h},p=[m,d];0<h.length&&p.push(f);var g={type:"tabpanel",tabs:p},w=a.windowManager.open({title:"Insert/Edit Media",size:"normal",body:g,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(e){var t,r,n=s(e);r=a,(t=n).embed=V(t.embed,t),t.embed&&re(t.source1)?ce(r,t.embed):te(r,t).then(function(e){ce(r,e.html)})["catch"](oe(r)),e.close()},onChange:function(e,t){switch(t.name){case"source1":l(e);break;case"embed":n=ne((r=e).getData()),(i=ae(a,n.embed)).dimensions={width:i.width?i.width:n.dimensions.width,height:i.height?i.height:n.dimensions.height},r.setData(ie(i))}var r,n,i},initialData:u})},se=function(e){return{showDialog:function(){ue(e)}}},le=function(e){e.addCommand("mceMedia",function(){ue(e)})},me=tinymce.util.Tools.resolve("tinymce.html.Node"),de=tinymce.util.Tools.resolve("tinymce.Env"),he=function(i,e){if(!1===P(i))return e;var o,a=B();return z({validate:!1,allow_conditional_comments:!1,special:"script,noscript",comment:function(e){a.comment(e)},cdata:function(e){a.cdata(e)},text:function(e,t){a.text(e,t)},start:function(e,t,r){if(o=!0,"script"!==e&&"noscript"!==e){for(var n=0;n<t.length;n++){if(0===t[n].name.indexOf("on"))return;"style"===t[n].name&&(t[n].value=i.dom.serializeStyle(i.dom.parseStyle(t[n].value),e))}a.start(e,t,r),o=!1}},end:function(e){o||a.end(e)}},G({})).parse(e),a.getContent()},fe=function(e,t){var r,n=t.name;return(r=new me("img",1)).shortEnded=!0,ge(e,t,r),r.attr({width:t.attr("width")||"300",height:t.attr("height")||("audio"===n?"30":"150"),style:t.attr("style"),src:de.transparentSrc,"data-mce-object":n,"class":"mce-object mce-object-"+n}),r},pe=function(e,t){var r,n,i,o=t.name;return(r=new me("span",1)).attr({contentEditable:"false",style:t.attr("style"),"data-mce-object":o,"class":"mce-preview-object mce-object-"+o}),ge(e,t,r),(n=new me(o,1)).attr({src:t.attr("src"),allowfullscreen:t.attr("allowfullscreen"),style:t.attr("style"),"class":t.attr("class"),width:t.attr("width"),height:t.attr("height"),frameborder:"0"}),(i=new me("span",1)).attr("class","mce-shim"),r.append(n),r.append(i),r},ge=function(e,t,r){var n,i,o,a,c;for(a=(o=t.attributes).length;a--;)n=o[a].name,i=o[a].value,"width"!==n&&"height"!==n&&"style"!==n&&("data"!==n&&"src"!==n||(i=e.convertURL(i,n)),r.attr("data-mce-p-"+n,i));(c=t.firstChild&&t.firstChild.value)&&(r.attr("data-mce-html",escape(he(e,c))),r.firstChild=null)},we=function(e){for(;e=e.parent;)if(e.attr("data-ephox-embed-iri")||(t=e.attr("class"))&&/\btiny-pageembed\b/.test(t))return!0;var t;return!1},ve=function(i){return function(e){for(var t,r,n=e.length;n--;)(t=e[n]).parent&&(t.parent.attr("data-mce-object")||("script"!==t.name||(r=N(_(i),t.attr("src"))))&&(r&&(r.width&&t.attr("width",r.width.toString()),r.height&&t.attr("height",r.height.toString())),"iframe"===t.name&&F(i)&&de.ceFalse?we(t)||t.replace(pe(i,t)):we(t)||t.replace(fe(i,t))))}},be=function(d){d.on("preInit",function(){var t=d.schema.getSpecialElements();A.each("video audio iframe object".split(" "),function(e){t[e]=new RegExp("</"+e+"[^>]*>","gi")});var r=d.schema.getBoolAttrs();A.each("webkitallowfullscreen mozallowfullscreen allowfullscreen".split(" "),function(e){r[e]={}}),d.parser.addNodeFilter("iframe,video,audio,object,embed,script",ve(d)),d.serializer.addAttributeFilter("data-mce-object",function(e,t){for(var r,n,i,o,a,c,u,s,l=e.length;l--;)if((r=e[l]).parent){for(u=r.attr(t),n=new me(u,1),"audio"!==u&&"script"!==u&&((s=r.attr("class"))&&-1!==s.indexOf("mce-preview-object")?n.attr({width:r.firstChild.attr("width"),height:r.firstChild.attr("height")}):n.attr({width:r.attr("width"),height:r.attr("height")})),n.attr({style:r.attr("style")}),i=(o=r.attributes).length;i--;){var m=o[i].name;0===m.indexOf("data-mce-p-")&&n.attr(m.substr(11),o[i].value)}"script"===u&&n.attr("type","text/javascript"),(a=r.attr("data-mce-html"))&&((c=new me("#text",3)).raw=!0,c.value=he(d,unescape(a)),n.append(c)),r.replace(n)}})}),d.on("setContent",function(){d.$("span.mce-preview-object").each(function(e,t){var r=d.$(t);0===r.find("span.mce-shim",t).length&&r.append('<span class="mce-shim"></span>')})})},ye=function(e){e.on("ResolveName",function(e){var t;1===e.target.nodeType&&(t=e.target.getAttribute("data-mce-object"))&&(e.name=t)})},xe=function(t){t.on("click keyup",function(){var e=t.selection.getNode();e&&t.dom.hasClass(e,"mce-preview-object")&&t.dom.getAttrib(e,"data-mce-selected")&&e.setAttribute("data-mce-selected","2")}),t.on("ObjectSelected",function(e){var t=e.target.getAttribute("data-mce-object");"audio"!==t&&"script"!==t||e.preventDefault()}),t.on("objectResized",function(e){var t,r=e.target;r.getAttribute("data-mce-object")&&(t=r.getAttribute("data-mce-html"))&&(t=unescape(t),r.setAttribute("data-mce-html",escape(V(t,{width:e.width,height:e.height}))))})},je=function(e){var t,r;e.ui.registry.addToggleButton("media",{tooltip:"Insert/edit media",icon:"embed",onAction:function(){e.execCommand("mceMedia")},onSetup:(t=e,r=["img[data-mce-object]","span[data-mce-object]","div[data-ephox-embed-iri]"],function(e){return t.selection.selectorChangedWithUnbind(r.join(","),e.setActive).unbind})}),e.ui.registry.addMenuItem("media",{icon:"embed",text:"Media...",onAction:function(){e.execCommand("mceMedia")}})};i.add("media",function(e){return le(e),je(e),ye(e),be(e),xe(e),se(e)}),function Oe(){}}();