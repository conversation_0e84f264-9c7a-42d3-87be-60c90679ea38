.amount {
	display: block;
	.inp-text {
		display: inline-block;
		width: 55px;
		padding: 0 8px;
		border: 1px solid $colorBg;
		color: $colorBlack;
		font-size: 14px;
		line-height: 38px;
		text-align: center;
	}
	&__wrap {
		display: flex;
		justify-content: flex-end;
	}
	&__value {
		flex: 0 0 55px;
		padding: 0 8px;
		border: 1px solid $colorBg;
		color: $colorBlack;
		font-size: 14px;
		line-height: 38px;
		text-align: center;
	}
	&__action {
		flex: 0 0 29px;
		height: 40px;
		border-top: 1px solid $colorBg;
		border-bottom: 1px solid $colorBg;
		background-color: $colorWhite;
		font-weight: 400;
		font-size: 14px;
		a {
			display: block;
			height: 100%;
			color: $colorBlack;
			line-height: 38px;
			text-align: center;
		}
	}
	&__action--minus {
		border-left: 1px solid $colorBg;
		border-top-left-radius: 10px;
		border-bottom-left-radius: 10px;
	}
	&__action--plus {
		border-right: 1px solid $colorBg;
		border-top-right-radius: 10px;
		border-bottom-right-radius: 10px;
	}

	@media ($lgUp) {
		&__wrap {
			justify-content: flex-start;
		}
	}
}
