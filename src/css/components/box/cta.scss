.b-cta {
	position: relative;
	display: flex;
	width: 100%;
	margin: 0;
	&__inner {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		padding: 30px;
		border-radius: $radius;
		background: $colorBg;
		color: $colorBlack;
		letter-spacing: -0.04em;
	}
	&__title {
		max-width: 450px;
		margin: 0 0 6px;
		color: $colorBlack;
		font-weight: bold;
		font-size: 30px;
		line-height: 1.2em;
		letter-spacing: -0.03em;
	}
	&__icon {
		display: none;
	}
	&__content {
		margin-bottom: 16px;
	}

	// MODIFICATIONS
	&:not(&--sm) &__inner {
		align-items: center;
	}
	&--sm &__inner {
		flex-direction: column;
		justify-content: center;
		> * {
			width: 100%;
		}
	}
	&--sm &__title {
		font-size: 32px;
		letter-spacing: 0.01em;
	}

	// HOVERS
	.hoverevents &__link:hover {
		color: $colorPrimary;
	}
	.hoverevents &__link:hover &__btn .btn__text {
		background: $colorHover;
	}

	// MEDIA QUERIES
	@media ($smUp) {
		&__inner {
			min-height: 240px;
			padding: 30px 82px 30px 150px;
		}
		&__icon {
			position: absolute;
			top: 50%;
			left: 38px;
			display: block;
			color: $colorPrimary;
			transform: translateY(-50%);
			.icon-svg {
				max-width: 73px;
			}
		}
		&--sm &__inner {
			padding: 30px 20px 30px 150px;
		}
		&--sm &__icon {
			left: 25px;
			width: 100px;
			text-align: center;
			&::before {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				z-index: 0;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: $colorWhite;
				transform: translate(-50%, -50%);
			}
			.icon-svg {
				position: relative;
				z-index: 1;
				max-width: 40px;
				max-height: 40px;
			}
			.icon-svg--map-point {
				max-width: 100%;
				max-height: 100%;
			}
		}
		&--sm &__content {
			margin-bottom: 16px;
		}
	}
	@media ($mdUp) {
		&__inner,
		&--sm &__inner {
			padding: 30px 20px 30px 210px;
		}
		&__content {
			margin-bottom: 0;
		}
		&__icon {
			left: 70px;
		}
		&:not(&--sm) &__content {
			flex: 1 0 100%;
		}
		&--sm &__icon {
			left: 60px;
		}
	}
	@media ($lgUp) {
		&__inner {
			flex-wrap: nowrap;
			height: 100%;
			min-height: 185px;
		}
		&:not(&--sm) &__content {
			flex: 1;
		}
		&--sm &__inner {
			min-height: 215px;
			padding: 30px 20px 30px 210px;
		}
	}
	@media ($xlUp) {
		&__inner {
			padding: 30px 82px 30px 255px;
		}
		&__icon {
			left: 140px;
		}
	}
}
