html {
	color: $colorText;
	font-family: $fontPrimary;
	font-size: $fontSizeMobile;
	line-height: (26/16);
	@media ($mdUp) {
		font-size: $fontSize;
		line-height: $lineHeight;
	}
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	// margin: 1.8em 0 0.9em;
	// margin: 1.8em 0 1.4em;
	margin: 1.8em 0 1.3em;
	color: $colorBlack;
	font-family: $fontSecondary;
	font-weight: bold;
	line-height: 1.2;
	letter-spacing: -0.02em;
}
h1,
.h1 {
	font-size: 24px;
	@media ($mdUp) {
		font-size: 52px;
	}
}
h2,
.h2 {
	font-size: 30px;
	@media ($mdUp) {
		font-size: 32px;
	}
}
h3,
.h3 {
	margin-bottom: 1.4em;
	font-size: 26px;
}
h4,
.h4 {
	margin-top: 1.2em;
	margin-bottom: 0.8em;
	font-size: 18px;
}
h5,
.h5 {
	font-size: 14px;
}
h6,
.h6 {
	font-size: 12px;
}

// Paragraph
p {
	margin: 0 0 $typoSpaceVertical;
}
hr {
	height: 1px;
	margin: $typoSpaceVertical 0;
	border: solid $colorBd;
	border-width: 1px 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 0 0 $typoSpaceVertical;
	padding: 0;
	p {
		margin-bottom: 0;
	}
}

// Links
a {
	color: $colorLink;
	text-decoration: none;
	transition: color $t;
	-webkit-tap-highlight-color: transparent;
	.hoverevents &:hover {
		color: $colorBlack;
	}
}

// Lists
li {
	margin: 0 0 1.2em;
	padding: 0 0 0 38px;
}
ul,
ol {
	margin: 0 0 ($typoSpaceVertical * 2);
	padding: 0;

	// line-height: (26 / 22);
	// font-size: 22px;
	list-style: none;
}
ul {
	li {
		background-image: url($svgPawOrange);
		background-position: 0 5px;

		// background-position: 0 3px;
		background-repeat: no-repeat;
		background-size: 18px 18px;
	}
	li > & {
		margin-bottom: 1.2em;
	}
	li > &:first-child {
		padding-top: 1em;
	}
}
ol {
	counter-reset: item;
	li {
		position: relative;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 5px;
			left: 0;
			color: $colorPrimary;
			font-weight: bold;
			font-size: 14px;
		}
	}
	ol {
		li {
			&::before {
				content: counter(item, lower-alpha) '.';
			}
		}
	}
	li > & {
		margin-bottom: 1.2em;
	}
	li > &:first-child {
		padding-top: 1em;
	}
}
dl {
	margin-bottom: $typoSpaceVertical;
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 ($typoSpaceVertical / 2);
	padding: 0;
}

// Tables
table {
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	margin: 0 0 $typoSpaceVertical;
	border: none;
	color: $colorBlack;
	font-size: $fontSize;
	@media ($lgDown) {
		width: auto !important;
		min-width: 100%;
	}
}
caption {
	padding: 0 0 10px;
	color: $colorBlack;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
th,
td {
	padding: 20px 0;
	p {
		margin-bottom: 0;
	}
}
thead th {
	text-align: left;
}
tr {
	border-top: 1px solid $colorGrayLight;
	&:last-child {
		border-bottom: 1px solid $colorGrayLight;
	}
}
tfoot th {
	text-align: left;
}

// Image
figure {
	display: inline-block;
	max-width: 620px;
	margin-bottom: $typoSpaceVertical;
	img {
		max-width: 100%;
		height: auto;
	}
}
figcaption {
	margin-top: 16px;
	color: $colorBlack;
	font-weight: bold;
}

img {
	@media ($xlDown) {
		max-width: 100%;
		height: auto;
	}
}

textarea {
	resize: none;
}

.inp-help {
	display: block;
	margin-top: 10px;
	font-size: 12px;
	line-height: 14px;
}
