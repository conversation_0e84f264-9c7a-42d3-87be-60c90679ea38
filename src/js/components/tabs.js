import $ from 'jquery';

export const init = () => {
	const TAB_KEY = 'subscription-tab';
	const ACCORDEON_KEY = 'subscription-tab-accordeons';
	const INTENT_KEY = 'subscription-tab-intent';

	$.fn.findButNotNested = function(selector, notInSelector) {
		var origElement = $(this);
		return origElement.find(selector).filter(function() {
			return origElement[0] == $(this).closest(notInSelector)[0];
		});
	};

	$(document).on('click', '.js-tabs__link', function (e) {
		e.preventDefault();

		const $this = $(this),
			$parent = $this.closest('.js-tabs'),
			$list = $($parent.find('.js-tabs__list')[0]),
			$links = $list.children('.js-tabs__item').children('.js-tabs__link'),
			$fragments = $($parent.find('.js-tabs__content')[0]),
			$tabs = $fragments.children('.js-tabs__fragment'),
			target = $this.attr('href');

		$links.removeClass('is-active');
		$tabs.removeClass('is-active');

		$this.addClass('is-active');
		$tabs.filter(target).addClass('is-active');
	});

	$(document).on('submit', '.b-subscription-tabs form', function () {
		const $form = $(this);
		const accordeonIndexes = [];

		$form.find('.js-accordeon__item.is-active').each(function () {
			const $item = $(this);
			const $allItems = $item.parent().children('.js-accordeon__item');
			const index = $allItems.index($item);
			if (index !== -1) {
				accordeonIndexes.push(index);
			}
		});

		const $tab = $form.closest('.js-tabs__fragment');
		const tabId = `#${$tab.attr('id')}`;

		if (tabId) {
			sessionStorage.setItem(TAB_KEY, tabId);
			sessionStorage.setItem(INTENT_KEY, 'true');
		}

		if (accordeonIndexes.length > 0) {
			sessionStorage.setItem(ACCORDEON_KEY, JSON.stringify(accordeonIndexes));
		} else {
			sessionStorage.removeItem(ACCORDEON_KEY);
		}
	});

	document.addEventListener('DOMContentLoaded', () => {
		const intent = sessionStorage.getItem(INTENT_KEY);
		const target = sessionStorage.getItem(TAB_KEY);

		if (intent && target) {
			const $tabLink = $(`.b-subscription-tabs .js-tabs__link[href="${target}"]`);
			if ($tabLink.length) {
				$tabLink.trigger('click');

				const accordeonData = sessionStorage.getItem(ACCORDEON_KEY);
				if (accordeonData) {
					const indexes = JSON.parse(accordeonData);
					const $fragment = $(target);
					indexes.forEach((i) => {
						const $item = $fragment.find('.js-accordeon__item').eq(i);
						if ($item.length) {
							const $head = $item.find('.js-accordeon__head');
							if ($head.length) {
								$head.trigger('click');
							}
						}
					});
				}

				const $form = $(target).find('form');
				if ($form.length) {
					$('html, body').animate({
							scrollTop: $form.offset().top - 100,
						},
						500,
					);
				}
			}

			sessionStorage.removeItem(TAB_KEY);
			sessionStorage.removeItem(ACCORDEON_KEY);
			sessionStorage.removeItem(INTENT_KEY);
		}
	});
};
