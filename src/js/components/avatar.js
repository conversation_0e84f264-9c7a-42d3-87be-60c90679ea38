import $ from 'jquery';
// import 'croppie/croppie';

export const init = ($target) => {
	$target.find('.f-avatar').each(function() {
		import('croppie/croppie').then(() => {
			// console.log('imported');

			var $this = $(this),
				$inpColor = $this.find('.f-avatar__inp-color'),
				$inpFile = $this.find('.f-avatar__inp-file'),
				$inpPositions = $this.find('.f-avatar__inp-positions'),
				$labelFile = $this.find('.f-avatar__label-file'),
				$btnRemove = $this.find('.f-avatar__btn--remove'),
				$crop = $this.find('.f-avatar__crop'),
				$sectionAvatar = $this.find('.f-avatar__section--avatar');

			const changeColor = (e) => {
				$sectionAvatar.find('.avatar').attr('class', 'f-avatar__avatar avatar avatar--' + $(e.target).val());
			};

			const handleFileUpload = (e) => {
				if (e.target.files && e.target.files[0]) {
					var fileName = e.target.files[0].name,
						$zoomUp = $this.find('.f-avatar__btn--zoomup'),
						$zoomOut = $this.find('.f-avatar__btn--zoomout');

					$this.addClass('has-file'); // switch to crop
					$this.removeClass('has-image'); // remove class when uploading new image
					$labelFile
						.removeClass('link-mask') // remove link-mask overlay
						.text(fileName); // set label name to file name

					// preview the uploaded file & Croppie
					var reader = new FileReader();
					reader.onload = function(e) {
						$crop.croppie('destroy'); // destroy previous image
						var curentZoom = 0,
							step = 0,
							crop = $crop.croppie({
								showZoomer: false,
								viewport: {
									width: $crop.width(),
									height: $crop.height(),
									type: 'circle',
								},
							});
						crop.croppie('bind', {
							url: e.target.result,
						});

						crop.on('update.croppie', function(ev, cropData) {
							var $slider = $this.find('.cr-slider');
							curentZoom = parseFloat($slider.attr('aria-valuenow'));
							step = curentZoom * 0.1;

							// set new crop positions
							$inpPositions.attr('value', crop.croppie('get').points.toString());
						});

						// zooming
						function scale(zoom) {
							crop.croppie('setZoom', zoom);
						}
						$zoomUp.on('click', function() {
							scale(curentZoom + step);
						});
						$zoomOut.on('click', function() {
							scale(curentZoom - step);
						});
					};
					reader.readAsDataURL(e.target.files[0]); // convert to base64 string
				}
			};

			const handleFileRemove = () => {
				$this.removeClass('has-file'); // switch to avatar
				$labelFile
					.addClass('link-mask') // add link-mask overlay
					.text($labelFile.data('placeholder')); // set back the file label placeholder
				$inpFile.val(''); // remove file from input
			};

			$inpColor.on('change', changeColor);
			$inpFile.on('change', handleFileUpload);
			$btnRemove.on('click', handleFileRemove);
		});
	});
};
