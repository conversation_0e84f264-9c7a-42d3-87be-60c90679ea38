UPDATE tree
SET customFieldsJson = JSON_SET(
	customFieldsJson,
	'$.parameterForFilter',
	JSON_ARRAY(
		JSON_OBJECT(
			'visibleParameters',
			JSON_ARRAY(
				JSON_OBJECT('parameter',120),
				JSO<PERSON>_OBJECT('parameter',116),
				JSON_OBJECT('parameter',117),
				JSON_OBJECT('parameter',113),
				JSON_OBJECT('parameter',124),
				JSON_OBJECT('parameter',118),
				JSON_OBJECT('parameter',78),
				JSON_OBJECT('parameter',121),
				JSON_OBJECT('parameter',122),
				JSON_OBJECT('parameter',127)
			)
		)
	)
					   )
WHERE uid = 'eshop' and parentId=1;

UPDATE tree
SET customFieldsJson = JSON_SET(
	customFieldsJson,
	'$.parameterForFilter',
	JSO<PERSON>_ARRAY(
		JSON_OBJECT(
			'visibleParameters',
			J<PERSON><PERSON>_ARRAY(
				JSON_OBJECT('parameter',120),
				JSO<PERSON>_OBJECT('parameter',116),
				JSO<PERSON>_OBJECT('parameter',117),
				JSON_OBJECT('parameter',113),
				JSON_OBJECT('parameter',124),
				JSON_OBJECT('parameter',118),
				JSON_OBJECT('parameter',78),
				JSON_OBJECT('parameter',121),
				JSON_OBJECT('parameter',122),
				JSON_OBJECT('parameter',127)
			)
		)
	)
					   )
WHERE uid = 'eshop' and parentId=3;

UPDATE tree
SET customFieldsJson = JSON_SET(
	customFieldsJson,
	'$.parameterForFilter',
	JSON_ARRAY(
		JSON_OBJECT(
			'visibleParameters',
			JSON_ARRAY(
				JSON_OBJECT('parameter',120),
				JSON_OBJECT('parameter',116),
				JSON_OBJECT('parameter',117),
				JSON_OBJECT('parameter',113),
				JSON_OBJECT('parameter',124),
				JSON_OBJECT('parameter',118),
				JSON_OBJECT('parameter',78),
				JSON_OBJECT('parameter',121),
				JSON_OBJECT('parameter',122),
				JSON_OBJECT('parameter',127)
			)
		)
	)
					   )
WHERE uid = 'eshop' and parentId=1359;


UPDATE tree
SET customFieldsJson = JSON_SET(
	customFieldsJson,
	'$.parameterForFilter',
	JSON_ARRAY(
		JSON_OBJECT(
			'visibleParameters',
			JSON_ARRAY(
				JSON_OBJECT('parameter',120),
				JSON_OBJECT('parameter',116),
				JSON_OBJECT('parameter',117),
				JSON_OBJECT('parameter',113),
				JSON_OBJECT('parameter',124),
				JSON_OBJECT('parameter',118),
				JSON_OBJECT('parameter',78),
				JSON_OBJECT('parameter',121),
				JSON_OBJECT('parameter',122),
				JSON_OBJECT('parameter',127)
			)
		)
	)
					   )
WHERE uid = 'eshop' and parentId NOT IN(1359, 1, 3);
